<template>
  <div class="member-manage">
    <TableToolTemp :toolListProps="toolListProps" class="log-tool" @handleTool="manageHandleTool"></TableToolTemp>

    <div class="nav-list">
      <div
        v-for="(item, index) in navBarlist"
        :key="index"
        :class="{ li: true, active: navActivited == index }"
        :style="{
          color: navActivited == index ? themeObj.color : '',
          'border-color': navActivited == index ? themeObj.color : '',
        }"
        @click="navChange(index)"
      >
        {{ item.name }}
      </div>
    </div>

    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
                @normalResetQuery="normalResetQuery"></SearchForm>
    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover>
      <el-table-column align="center" prop="opportunityName" label="机会名称" v-if="this.navActivited==1" width="180px"></el-table-column>
      <el-table-column align="center" prop="id" label="机会ID" v-if="this.navActivited==1"></el-table-column>

      <el-table-column align="center" prop="agentName" label="服务顾问"></el-table-column>
      <el-table-column align="center" prop="companyName" label="所属机构" width="160px"></el-table-column>
      <el-table-column align="center" prop="salesCenterName" label="所属营业部" width="200px"></el-table-column>
      <el-table-column align="center" prop="projectManagerName" label="项目经理" v-if="this.navActivited==1"></el-table-column>
      <el-table-column align="center" prop="teamTime" label="组队完成时间" v-if="this.navActivited==1" width="160px"></el-table-column>
      <el-table-column align="center" prop="logTime" label="最新项目日志时间" v-if="this.navActivited==1" width="160px"></el-table-column>

      <el-table-column align="center" prop="enterpriseName" label="企业名称" width="220px"></el-table-column>
      <el-table-column align="center" prop="creditCode" label="社会统一信用代码" width="180px"></el-table-column>
      <el-table-column align="center" prop="dtType" label="企业类型">
        <template slot-scope="scope">
          <div>{{ dtTypes | getDtTypeName(scope.row.dtType) }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="verifyTime" label="第三方报告查询时间" v-if="this.navActivited==0" width="160px"></el-table-column>
      <el-table-column align="center" prop="kycReportTime" label="KYC报告查询时间" v-if="this.navActivited==0" width="160px"></el-table-column>

      <el-table-column align="center" prop="generalInsuranceType" label="客户需求" v-if="this.navActivited==1"></el-table-column>
      <el-table-column align="center" prop="opportunityType" label="业务渠道" v-if="this.navActivited==1">
        <template slot-scope="scope">
          <div>{{ opportunityTypes | getOpportunityTypeName(scope.row.opportunityType) }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="insureNum" label="预估投保人数" v-if="this.navActivited==1" width="160px" ></el-table-column>
      <el-table-column align="center" prop="premiumBudget" label="保费预算" v-if="this.navActivited==1" width="160px"></el-table-column>
      <el-table-column align="center" prop="isBid" label="是否需要投标" v-if="this.navActivited==1" width="160px">
        <template slot-scope="scope">
          {{scope.row.isBid|getDicItemName("gen.yesorno.num")}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="submitTime" label="提交时间" v-if="this.navActivited==1"  width="160px"></el-table-column>
      <el-table-column align="center" prop="status" label="机会状态" v-if="this.navActivited==1">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 1">
            {{scope.row.processStep}}
          </span>
          <span v-else-if="scope.row.status == 4">
            {{ scope.row.status | getDicItemName("elms.opportunity.status") }} - {{ closeReasonTypes | getCloseReasonDesc(scope.row.closeReasonType) }}
          </span>
          <span v-else>
            {{ scope.row.status | getDicItemName("elms.opportunity.status") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150px">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="detail(scope.row)" >机会详情</el-button>
          <el-button class="btn-center" type="text"  @click="submitOpp(scope.row)" v-if="navActivited==0" >提交机会</el-button>
<!--          <el-button class="btn-center" type="text" @click="handleViewQichachaReport(scope.row)"   v-if="navActivited==0" >第三方信息</el-button>-->
<!--          <el-button class="btn-center" type="text" @click="handleViewReport(scope.row.kycReportUrl)" v-if="navActivited==0 && scope.row.kycReportUrl" >KYC信息</el-button>-->

        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
                :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>



  </div>
</template>

<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import Pagination from "@/components/layouts/Pagination";
import {getDicItemList} from "@/config/tool";
import fileExport from "@/utils/fileExport";
import {findCompanyOrgData, findLegalOrgData} from "@/api/userManagement/index.js";
import {
  kbcPath
} from "@/utils/globalParam";
import {
  detailPage,
  getOpportunityStatus,
  findLegalOrgDataByTenantId,
  countUserParticipatedOpportunities,
  changeManager, submitOpportunity
} from "../../api/workbench";
import {getBranchUsers, getLegalList} from "@/api/processManagement";

export default {
  name: "opportunityManage",
  data() {
    return {

      navBarlist: [
        { name: "未提交" },
        { name: "已提交" },
      ],
      toolListProps: {
        toolTitle: "机会管理",
        toolList: [
          {
            name: "导出",
            btnCode: ""
          }
        ]
      },
      navActivited:0,
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          activited: 0,


        }
      },
      tempParam:{},
      searchFormTemp: [],
      actOneSearch:[ {
        label: "顾问姓名/用户名",
        name: "agentName",
        type: "input",
        width: "200px"
      },
        {
          label: "所属机构",
          name: "legalCode",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "企业名称",
          name: "enterpriseName",
          type: "input",
          width: "200px"
        },
        {
          label: "企业代码",
          name: "creditCode",
          type: "input",
          width: "200px"
        },
        {
          label: "业务渠道",
          name: "opportunityType",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "仅验真",
          name: "isVerified",
          type: "select",
          width: "200px",
          list:[]
        }],
      actTwoSearch:[{
        label: "顾问姓名/用户名",
        name: "agentName",
        type: "input",
        width: "200px"
      },
        {
          label: "所属机构",
          name: "legalCode",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "企业名称",
          name: "enterpriseName",
          type: "input",
          width: "200px"
        },
        {
          label: "企业代码",
          name: "creditCode",
          type: "input",
          width: "200px"
        },
        {
          label: "机会名称",
          name: "opportunityName",
          type: "input",
          width: "200px"
        },
        {
          label: "机会ID",
          name: "opportunityId",
          type: "input",
          width: "200px"
        },
        {
          name: "createTime",
          type: "doubleDate",
          label: "机会提交时间",
          placeholder: "请选择",
          elType: "DateTimePicker",
          options: [
            {
              name: "submitBeginTime",
              placeholder: "请输入开始时间",
              value: "",
            },
            {
              name: "submitEndTime",
              placeholder: "请输入结束时间",
              value: "",
            },
          ],
          fixedShow: true,
        },
        {
          label: "企业类型",
          name: "dtType",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "机会状态",
          name: "processStep",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "业务渠道",
          name: "opportunityType",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "仅验真",
          name: "isVerified",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "项目经理",
          name: "name",
          type: "input",
          width: "200px"
        },
        {
          name: "teamTime",
          type: "doubleDate",
          label: "组队完成时间",
          placeholder: "请选择",
          elType: "DateTimePicker",
          options: [
            {
              name: "teamBeginTime",
              placeholder: "请输入开始时间",
              value: "",
            },
            {
              name: "teamEndTime",
              placeholder: "请输入结束时间",
              value: "",
            },
          ],
          fixedShow: true,
        },
        {
          name: "logTime",
          type: "doubleDate",
          label: "最新项目日志时间",
          placeholder: "请选择",
          elType: "DateTimePicker",
          options: [
            {
              name: "logBeginTime",
              placeholder: "请输入开始时间",
              value: "",
            },
            {
              name: "logEndTime",
              placeholder: "请输入结束时间",
              value: "",
            },
          ],
          fixedShow: true,
        }],

      tableData: [{}],
      total: 0,
      legalList: [],
      dtTypes:[{dicItemCode:"A",dicItemName:"央企"},{dicItemCode:"B",dicItemName:"上市公司"},{dicItemCode:"C",dicItemName:"大型企业"},{dicItemCode:"D",dicItemName:"中小企业"}],
      verifiedTypes:[{dicItemCode:"1",dicItemName:"是"},{dicItemCode:"0",dicItemName:"否"}],
      opportunityTypes:[{dicItemCode:"1",dicItemName:"员服"},{dicItemCode:"2",dicItemName:"综合"}],
      closeReasonTypes:[{dicItemCode:1,dicItemName:"机会已成交"},{dicItemCode:2,dicItemName:"机会推进失败"},{dicItemCode:3,dicItemName:"无效机会"}],
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  filters: {
    getDtTypeName(list, dtType) {

      let index = list.findIndex((v) => v.dicItemCode == dtType);
      return index > -1 ? list[index].dicItemName : "";
      return "";
    },
    getOpportunityTypeName(list, opportunityType) {

      let index = list.findIndex((v) => v.dicItemCode == opportunityType);
      return index > -1 ? list[index].dicItemName : "";
      return "";
    },
    getCloseReasonDesc(list, closeReasonType) {
      let index = list.findIndex((v) => v.dicItemCode == closeReasonType);
      return index > -1 ? list[index].dicItemName : "";
    }
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    // 获取企客用户权限信息
    qikeUserInfo() {
      return this.$store.getters["layoutStore/getQikeUserInfo"];
    }
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    navChange(num) {
      this.navActivited = num;
      // 重置查询条件
      // this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initParam.param.activited = num;
      this.normalResetQuery();
    },

    manageHandleTool(item){
      if (item.name == "导出") {
        const {tenantId, access_token, funcId} = this.currentLoginUser;
        console.log(access_token,"access_token");
        let param = this._.cloneDeep(this.tempParam.param);
        let exportUrl = `/api/opportunity/export?access_token=${access_token}&funcId=${funcId}&tenantId=${tenantId}`;
        fileExport("机会数据.xls", param, exportUrl);
      }
    },

    handleViewReport(url){
      window.open(url,'_blank');
    },

    handleViewQichachaReport(row){
      console.log(row.creditCode);
      if (window.parent && window.parent.kbcHopRouting) {
        const creditCode = row.creditCode;
        const name = row.enterpriseName;
        const obj = {
          rootPath:"kbc-elms",
          routePath:"enterprise/info",
          customFuncName:name+"-信息详情",
          postChildUrl:`iframe?iframeUrl=${kbcPath}kbc-elms/enterprise/third-party-query/${creditCode}?creditCode=${creditCode}`,
          customId:creditCode,
        }
        window.parent.kbcHopRouting(obj);
      }

    },


    // 检查用户是否有指定权限
    hasPermission(permissionCode) {

      if (!this.qikeUserInfo || !this.qikeUserInfo.roleAuthStr) {
        return false;
      }
      const roleAuthStr = this.qikeUserInfo.roleAuthStr;
      const permissions = roleAuthStr.split(',').map(item => item.trim());
      return permissions.includes(permissionCode);
      // return true;

    },


    initData() {
      this.getDicFun();
      this.getDetailPage();
    },
    async getDetailPage(){

      let param = _.cloneDeep(this.initParam);

      if(param.param.processStep){
        let ss = param.param.processStep;
        param.param.opportunityStatus = parseInt(ss.split("-")[0]);
        param.param.processStep = ss.split("-")[1];
      }
      if(this.navActivited==0){
        // 未提交，只查未提交的机会
        param.param.status=[0];
      } else {
        // 已提交
        param.param.status=[1,2,3,4];
      }

      this.tempParam = param;
      const res = await detailPage(param);
      if (res) {
        this.total = res.total;
        this.tableData = [];
        if (res.list) {
          this.tableData = res.list ? res.list : [{}];
        }
      }
    },

    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    },

    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.searchFormTemp = this.$options.data().searchFormTemp;
      this.initData();
      // this.getDicFun();
    },
    async getDicFun() {
      if(this.navActivited==0){
        this.searchFormTemp=this.actOneSearch;
      } else {
        this.searchFormTemp=this.actTwoSearch;
      }


      await getDicItemList("elms.opportunity.status");
      await getDicItemList("gen.yesorno.num");

      // 机构
      let legalListTemp = await findLegalOrgData({});
      if(legalListTemp){
        legalListTemp.forEach(item => {
          this.legalList.push({
            dicItemName: item.orgName,
            dicItemCode: item.orgCode
          });
        });
        // 所属机构
        this.searchFormTemp[1].list = this.legalList;
        this.searchFormTemp[4].list = this.opportunityTypes;
        this.searchFormTemp[5].list = this.verifiedTypes;

      }

      if(this.navActivited==1){
        this.searchFormTemp[7].list = this.dtTypes;

        const statusList = await getOpportunityStatus({});
        if(statusList){
          console.log(statusList,"statusList");
          // 状态
          this.searchFormTemp[8].list = statusList;
        }

        // // 业务渠道
        this.searchFormTemp[9].list = this.opportunityTypes;
        this.searchFormTemp[10].list = this.verifiedTypes;
      }

    },

    detail(row) {
      this.$router.push({
        name: "opportunityDetails",
        query: {
          id: row.id,
          title:"机会管理"
        }
      });
    },

    async submitOpp(row){
      let param  = {opportunityId:row.id};
      const res = await submitOpportunity(param);
      if (res) {
        this.$message.success(res.resp_msg);
        this.initData();
      }
    }

  }
};
</script>

<style lang="less" scoped>

.popup-content {
  padding: 0 20px 20px 20px;
  max-height: 600px;
  overflow-y: auto;

  // 在小屏幕上调整最大高度
  @media (max-height: 800px) {
    max-height: 500px;
  }

  @media (max-height: 600px) {
    max-height: 400px;
  }

  .business-type-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 4px;

    .section-title {
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 12px;
      color: #333;
    }

    .el-radio-group {
      .el-radio {
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .table-container {
    margin-top: 10px;
    margin-bottom: 10px;
    max-height: 300px;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    // Firefox
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;

    // IE
    -ms-overflow-style: auto;

    // 为表格设置固定高度和滚动
    .el-table {
      .el-table__body-wrapper {
        overflow-y: auto;
      }

      // 确保表格在小屏幕上也能正常显示
      .el-table__header-wrapper {
        background-color: #f5f7fa;
      }

      .el-table__body {
        .el-table__row {
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }

  .restart-close-tip {
    margin-bottom: 20px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 4px;

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .form-actions {
    margin-top: 20px;
    padding-top: 20px;
    text-align: center;
  }
}

.location-box {
  width: 360px;
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  cursor: pointer;
  margin: 5px 5px 5px 5px;

  .left {
    cursor: pointer;

    .location {
      margin: 5px 5px 5px 5px;
    }
  }

  .right {
    cursor: pointer;
  }
}
.member-manage {
  .text-content{
    width: 100%;
    text-align: left;
  }
  .text-content div{
    margin: 5px 5px 5px 5px;
  }

  .communication-item {
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      i {
        margin-right: 2px;
      }
    }
  }

  .log-form {
    padding-bottom: 20px;
    height: 650px; /* 设置弹窗固定高度 */
    display: flex;
    flex-direction: column;

    .form-content {
      flex: 1;
      overflow-y: auto; /* 添加滚动条 */
      padding-right: 10px; /* 为滚动条留出空间 */
      margin-bottom: 20px;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }

      /* Firefox 隐藏滚动条 */
      scrollbar-width: none;

      /* IE 隐藏滚动条 */
      -ms-overflow-style: none;
    }

    .upload-section {
      margin-top: 10px;

      .upload-area {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        text-align: center;
        background: #fafafa;
        cursor: pointer;
        transition: border-color 0.3s;

        .upload-placeholder {
          color: #8c939d;
          font-size: 14px;

          i {
            font-size: 28px;
            margin-bottom: 8px;
            display: block;
          }
        }
      }

      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;

        .image-item {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e4e7ed;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .image-actions {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
              background: rgba(0, 0, 0, 0.8);
            }

            i {
              color: #fff;
              font-size: 12px;
            }
          }
        }
      }
    }

    .form-actions {
      flex-shrink: 0; /* 防止按钮区域被压缩 */
      padding-top: 20px;
      text-align: center;
      border-top: 1px solid #e4e7ed;
      background: #fff;
    }
  }

  .delete-confirm {
    padding: 0 20px 20px 20px;
    text-align: center;

    .confirm-content {
      margin-bottom: 20px;

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }


  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;
    margin-bottom: 20px;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }
}

// 选中人员信息显示样式
.selected-user-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f2f5;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .info-item {
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 200px;

    .label {
      font-weight: bold;
      margin-right: 8px;
      min-width: 80px;
    }

    .value {
      color: v-bind('themeObj.color');
      flex: 1;
    }
  }

  // 在小屏幕上调整布局
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 10px;

    .info-item {
      flex-direction: column;
      align-items: flex-start;
      min-width: auto;

      .label {
        margin-bottom: 4px;
        min-width: auto;
      }
    }
  }
}

// 重启原因输入样式
.restart-reason-section {
  width: 100%;
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .el-form-item {
    margin-bottom: 0;
  }

  .el-textarea {
    .el-textarea__inner {
      border: 1px solid #dcdfe6;
      border-radius: 4px;

      &:focus {
        border-color: #409eff;
      }
    }
  }
}

</style>
