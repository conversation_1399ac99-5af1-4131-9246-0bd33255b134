<template>
  <div class="base-info">
    <div class="tool-wrap">
      <el-button type="primary" v-for="(item, index) in toolList" :key="index" @click="handleTool(item)">{{ item.name
      }}</el-button>
    </div>
    <TableToolTemp :toolListProps="{ toolTitle: '机会信息' }" class="log-tool"></TableToolTemp>
    <div class="info-item">
      <div class="item">机会名称：{{ (opportunityDetail.opportunity && opportunityDetail.opportunity.opportunityName) || '-'
        }}</div>
      <div class="item">机会ID：{{ (opportunityDetail.opportunity && opportunityDetail.opportunity.id) || '-' }}</div>
      <div class="item">提交时间：{{ formatDate(opportunityDetail.opportunityDetail &&
        opportunityDetail.opportunityDetail.submitTime) }}</div>
      <div class="item">业务渠道：{{ (opportunityDetail.opportunity && opportunityDetail.opportunity.opportunityType == '1')
        ? '员福' : '综合' || '-' }}</div>
      <div class="item">机会状态：{{ getOpportunityStatusText(opportunityDetail.opportunity &&
        opportunityDetail.opportunity.status) }}</div>
      <div class="item">历史保单：{{ (opportunityDetail.opportunityDetail &&
        opportunityDetail.opportunityDetail.hasHistoryPolicy === 1) ? '是' : '否' }}</div>
      <div class="item">历史保单到期时间：{{ formatDate(opportunityDetail.opportunityDetail &&
        opportunityDetail.opportunityDetail.policyExpireTime) }}</div>
      <div class="item">期望保障生效时间：{{ formatDate(opportunityDetail.opportunityDetail &&
        opportunityDetail.opportunityDetail.submitTime) }}</div>
      <div class="item">保费预算：{{ formatAmount(opportunityDetail.opportunityDetail &&
        opportunityDetail.opportunityDetail.premiumBudget) }}</div>
      <div class="item">预估投保人数：{{ (opportunityDetail.opportunityDetail && opportunityDetail.opportunityDetail.insureNum)
        ?
        `${opportunityDetail.opportunityDetail.insureNum}人` : '-' }}</div>
      <div class="item">顾问姓名：{{ (opportunityDetail.opportunity && opportunityDetail.opportunity.agentName) || '-' }}
      </div>
      <div class="item">顾问工号：{{ (opportunityDetail.opportunity && opportunityDetail.opportunity.agentCode) || '-' }}
      </div>
      <div class="item">所属机构：{{ (opportunityDetail.opportunity && opportunityDetail.opportunity.companyName) || '-' }}
      </div>
      <div class="item">是否需要投标：{{ (opportunityDetail.opportunityDetail && opportunityDetail.opportunityDetail.isBid ==
        1) ? '是' : '否' }}</div>
      <div class="item">投标结束时间：{{ formatDate(opportunityDetail.opportunityDetail &&
        opportunityDetail.opportunityDetail.bidEndDate) }}</div>
      <div class="item">客户需求：{{ '-' }}</div>
    </div>
    <div class="item-tips">备注：{{ (opportunityDetail.opportunityDetail && opportunityDetail.opportunityDetail.remark) ||
      '-' }}</div>

    <TableToolTemp :toolListProps="{ toolTitle: '客户信息' }" class="log-tool"></TableToolTemp>
    <div class="info-item">
      <div class="item">企业名称：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.name) || '-' }}</div>
      <div class="item">社会统一信用代码：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.creditCode) || '-' }}
      </div>
      <div class="item">企业所在城市：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.city) || '-' }}</div>
      <div class="item">企业所属行业：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.categoryName) || '-' }}
      </div>
      <div class="item">企业年收入：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.annualIncome) || '-' }}
      </div>
      <div class="item">企业人员规模：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.staffScale) || '-' }}
      </div>
      <div class="item">企业类型：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.dtType) || '-' }}</div>
      <div class="item">企业对接人姓名：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.enterpriseContacter) ||
        '-' }}</div>
      <div class="item">企业对接人电话：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.contacterPhone) || '-'
        }}</div>
      <div class="item">企业对接人职务：{{ (opportunityDetail.opportunityDetail &&
        opportunityDetail.opportunityDetail.contacterPost) || '-' }}</div>
      <div class="item">KYC报告：<span class="link-text" v-if="opportunityDetail.opportunityDetail && opportunityDetail.opportunityDetail.kycReportUrl" @click="handleViewReport(opportunityDetail.opportunityDetail.kycReportUrl)">查看详情</span></div>
      <div class="item">KYC 报告生成时间：{{ (opportunityDetail.opportunityDetail && opportunityDetail.opportunityDetail.kycReportTime) ||'-' }}</div>
      <div class="item">风险评估报告：<span class="link-text" v-if="opportunityDetail.opportunityDetail && opportunityDetail.opportunityDetail.riskReportUrl" @click="handleViewReport(opportunityDetail.opportunityDetail.riskReportUrl)">查看详情</span></div>
      <div class="item">风险评估报告生成时间：{{ (opportunityDetail.opportunityDetail && opportunityDetail.opportunityDetail.riskReportTime) ||'-' }}</div>
      <div class="item">风险评估结果：{{ opportunityDetail.opportunityDetail && opportunityDetail.opportunityDetail.riskReportTime?"通过":'-' }}</div>
      <div class="item">第三方校验信息：<span class="link-text" @click="handleViewQichachaReport">企查查报告</span></div>
      <div class="item">第三方报告查询时间：{{ (opportunityDetail.enterprise && opportunityDetail.enterprise.verifyTime) ||'-' }}</div>
    </div>
    <div class="step-wrap">
      <el-steps :active="active">
        <el-step v-for="(item, index) in processList" :key="index" :title="item.name"></el-step>
      </el-steps>

      <div class="step-list">
        <div class="step-item" v-for="(item, index) in stepList" :key="index">
          <div class="radius" :style="{ background: themeObj.color }"></div>
          <div class="item-date" v-if="item.timelineTime">{{ item.timelineTime }}</div>

          <!-- 历史记录 -->
          <div class="step-info-wrap">
            <div class="info-title" v-html="item.description"></div>



            <!-- 任务块列表 - 只在type为PROGRESS时渲染 -->
            <div class="task-blocks"
              v-if="item.type == 'PROGRESS' && item.customProperties && item.customProperties.dolist">
              <div class="task-block">
                <div class="task-title">{{ item.activityName }}</div>
                <div class="task-content">
                  <!-- 需执行事项 -->
                  <div class="execution-items"
                    v-if="item.customProperties.dolist && item.customProperties.dolist.length">
                    <div v-for="action in item.customProperties.dolist" :key="action.id" class="action-row">
                      <span class="item-label">需执行事项：</span>
                      <span class="action-item">{{ action.name }}</span>
                      <span class="required-text">({{ action.required ? '必填' : '可选' }})</span>
                    </div>
                  </div>

                      <!-- 确认完成按钮 - 根据步骤状态控制显示和禁用 -->
                     <div class="task-completion" v-if="shouldShowTaskCompletionButton(item, index)">
                       <el-button type="primary" size="small"
                         :disabled="taskCompletionStatus['task'] || !isCurrentStep(item, index) || hasAssignProjectManager(item)"
                         @click="confirmTaskCompletion('task', item.customProperties)"
                         :loading="taskLoadingStatus['task']">
                         确认已完成
                       </el-button>
                     </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 领取机会确认弹窗 -->
    <DtPopup :isShow.sync="showClaimPopup" @close="closeClaimPopup" title="领取机会" center :footer="false" width="600px">
      <div class="claim-confirm">
        <div class="confirm-content">
          <p>领取成功后，该机会归属您来跟进，其他人员不可见，确认是否领取该机会？</p>
        </div>
        <div class="confirm-actions">
          <el-button @click="closeClaimPopup">取消</el-button>
          <el-button type="primary" @click="confirmClaim" :loading="claimLoading">确认领取</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 暂停机会弹窗 -->
    <DtPopup :isShow.sync="showPausePopup" @close="closePausePopup" title="暂停机会" center :footer="false" width="600px">
      <div class="pause-form">
        <div class="description-content">
          <p>如遇一些特殊原因导致该机会暂时无法推进,可通过本功能标记为"暂停状态",该状态和原因会同步至所有项目参与人员可见,暂停的机会可以随时开启。</p>
        </div>
        <el-form ref="pauseFormRef" :model="pauseForm" :rules="pauseRules" label-width="0">
          <el-form-item prop="reason" required>
            <el-input type="textarea" v-model="pauseForm.reason" placeholder="请输入暂停原因 (必填)" :rows="4" maxlength="150"
              show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
        <div class="form-actions">
          <el-button @click="closePausePopup">取消</el-button>
          <el-button type="primary" @click="confirmPause" :loading="pauseLoading">确认暂停</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 启动机会弹窗 -->
    <DtPopup :isShow.sync="showRestartPopup" @close="closeRestartPopup" title="重启机会" center :footer="false"
      width="600px">
      <div class="restart-form">
        <div class="description-content">
          <p>确认重启后,该机会将恢复至正常推进状态,同时所有项目参与人员可见,是否确认本次操作?</p>
        </div>
        <div class="form-actions">
          <el-button @click="closeRestartPopup">取消</el-button>
          <el-button type="primary" @click="confirmRestart" :loading="restartLoading">确认重启</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 关闭机会弹窗 -->
    <DtPopup :isShow.sync="showClosePopup" @close="closeClosePopup" title="关闭机会" center :footer="false" width="600px">
      <div class="close-form">
        <el-form ref="closeFormRef" :model="closeForm" :rules="closeRules">
          <el-form-item prop="closeReasonType" required>
            <el-radio-group v-model="closeForm.closeReasonType">
              <el-radio label="1">机会已成交</el-radio>
              <el-radio label="2">机会推进失败</el-radio>
              <el-radio label="3">无效机会</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="关闭原因" prop="closeReason" :rules="closeReasonRules">
            <el-input type="textarea" v-model="closeForm.closeReason" :placeholder="closeReasonPlaceholder" :rows="4"
              maxlength="150" show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
        <div class="form-actions">
          <el-button @click="closeClosePopup">取消</el-button>
          <el-button type="primary" @click="confirmClose" :loading="closeLoading">确认关闭</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 重启关闭机会弹窗 -->
    <DtPopup :isShow.sync="showRestartClosePopup" @close="closeRestartClosePopup" title="重启机会" center :footer="false"
      width="900px">
      <div class="popup-content">
        <!-- 提示文案 -->
        <div class="restart-close-tip" v-if="closeReasonInfo">
          <p :style="{ color: themeObj.color }">
            该机会已被 {{ closeReasonInfo.operatorOrgPath }} {{ closeReasonInfo.operatorUserName }}（{{
              closeReasonInfo.operatorName }}）变更为"已关闭-{{ closeReasonInfo.reasonTypeName }}"，是否确认重启并指派新的项目经理？
          </p>
        </div>

        <!-- 业务主体选择 -->
        <div class="business-type-section">
          <div class="section-title">业务主体</div>
          <el-radio-group v-model="restartCloseBusinessType" @change="handleRestartCloseBusinessTypeChange">
            <el-radio label="T0002">经纪</el-radio>
            <el-radio label="T0001">销售</el-radio>
          </el-radio-group>
        </div>

        <SearchForm @changeSelect="handleRestartCloseSearch" :searchForm="restartCloseSearchForm" :showSearch="false"
          :searchFormTemp="restartCloseSearchFormTemp" />
        <div class="table-container">
          <el-table :data="restartCloseUserList" style="width: 100%;" @current-change="handleRestartCloseUserSelect">
            <el-table-column label="" width="50" align="center">
              <template slot-scope="scope">
                <input type="radio" :name="'restartCloseUserSelect'" :value="scope.row.userId"
                  v-model="selectedRestartCloseUserId" style="margin: 0;">
              </template>
            </el-table-column>
            <el-table-column prop="nickName" label="人员姓名" align="center" min-width="120" />
            <el-table-column prop="phone" label="手机号" align="center" min-width="130" />
            <el-table-column prop="email" label="邮箱" align="center" min-width="180" />
            <el-table-column prop="organName" label="人员归属" align="center" min-width="120" />
          </el-table>
        </div>

        <!-- 选中人员信息显示 -->
        <div class="selected-user-info" v-if="selectedRestartCloseUserInfo">
          <div class="info-item">
            <span class="label">人员姓名：</span>
            <span class="value">{{ selectedRestartCloseUserInfo.nickName }}</span>
          </div>
          <div class="info-item">
            <span class="label">参与机会数：</span>
            <span class="value">{{ selectedRestartCloseUserInfo.opportunityCount }}</span>
          </div>
          <div class="info-item">
            <span class="label">待完成任务数：</span>
            <span class="value">{{ selectedRestartCloseUserInfo.taskCount }}</span>
          </div>
        </div>

        <!-- 重启原因输入 -->
        <div class="restart-reason-section">
          <el-form ref="restartCloseFormRef" :model="restartCloseForm" :rules="restartCloseRules" label-width="0">
            <el-form-item prop="reasonDesc" required>
              <el-input type="textarea" v-model="restartCloseForm.reasonDesc" placeholder="请输入重启原因 (必填)" :rows="4"
                maxlength="150" show-word-limit>
              </el-input>
            </el-form-item>
          </el-form>
        </div>

        <div class="form-actions">
          <el-button @click="closeRestartClosePopup">取消</el-button>
          <el-button type="primary" @click="confirmRestartClose" :loading="restartCloseLoading">确认重启</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 指派统筹弹窗 -->
    <DtPopup :isShow.sync="showAssignPopup" @close="closeAssignPopup" title="指派统筹" center :footer="false" width="900px">
      <div class="assign-content">
        <SearchForm @changeSelect="handleAssignSearch" :searchForm="assignSearchForm" :showSearch="false"
          :searchFormTemp="assignSearchFormTemp" />
        <div class="table-container">
          <el-table :data="assignUserList" style="width: 100%;" @current-change="handleAssignUserSelect">
            <el-table-column label="" width="50" align="center">
              <template slot-scope="scope">
                <input type="radio" :name="'assignUserSelect'" :value="scope.row.userId" v-model="selectedAssignUserId"
                  style="margin: 0;">
              </template>
            </el-table-column>
            <el-table-column prop="nickName" label="人员姓名" align="center" min-width="120" />
            <el-table-column prop="phone" label="手机号" align="center" min-width="130" />
            <el-table-column prop="email" label="邮箱" align="center" min-width="180" />
            <el-table-column prop="organName" label="人员归属" align="center" min-width="120" />
          </el-table>
        </div>

        <!-- 选中人员信息显示 -->
        <div class="selected-user-info" v-if="selectedUserInfo">
          <div class="info-item">
            <span class="label">人员姓名：</span>
            <span class="value">{{ selectedUserInfo.nickName }}</span>
          </div>
          <div class="info-item">
            <span class="label">参与机会数：</span>
            <span class="value">{{ selectedUserInfo.opportunityCount }}</span>
          </div>
          <div class="info-item">
            <span class="label">待完成任务数：</span>
            <span class="value">{{ selectedUserInfo.taskCount }}</span>
          </div>
        </div>

        <div class="form-actions">
          <el-button @click="closeAssignPopup">取消</el-button>
          <el-button type="primary" @click="confirmAssign" :loading="assignLoading">确认指派</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 指派项目经理弹窗 -->
    <DtPopup :isShow.sync="showManagerPopup" @close="closeManagerPopup" title="指派项目经理" center :footer="false"
      width="900px">
      <div class="popup-content">
        <!-- 业务主体选择 -->
        <div class="business-type-section">
          <div class="section-title">业务主体</div>
          <el-radio-group v-model="managerBusinessType" @change="handleManagerBusinessTypeChange">
            <el-radio label="T0002">经纪</el-radio>
            <el-radio label="T0001">销售</el-radio>
          </el-radio-group>
        </div>

        <SearchForm @changeSelect="handleManagerSearch" :searchForm="managerSearchForm" :showSearch="false"
          :searchFormTemp="managerSearchFormTemp" />
        <div class="table-container">
          <el-table :data="managerUserList" style="width: 100%;" @current-change="handleManagerUserSelect">
            <el-table-column label="" width="50" align="center">
              <template slot-scope="scope">
                <input type="radio" :name="'managerUserSelect'" :value="scope.row.userId"
                  v-model="selectedManagerUserId" style="margin: 0;">
              </template>
            </el-table-column>
            <el-table-column prop="nickName" label="人员姓名" align="center" min-width="120" />
            <el-table-column prop="phone" label="手机号" align="center" min-width="130" />
            <el-table-column prop="email" label="邮箱" align="center" min-width="180" />
            <el-table-column prop="organName" label="人员归属" align="center" min-width="120" />
          </el-table>
        </div>

        <!-- 选中人员信息显示 -->
        <div class="selected-user-info" v-if="selectedManagerUserInfo">
          <div class="info-item">
            <span class="label">人员姓名：</span>
            <span class="value">{{ selectedManagerUserInfo.nickName }}</span>
          </div>
          <div class="info-item">
            <span class="label">参与机会数：</span>
            <span class="value">{{ selectedManagerUserInfo.opportunityCount }}</span>
          </div>
          <div class="info-item">
            <span class="label">待完成任务数：</span>
            <span class="value">{{ selectedManagerUserInfo.taskCount }}</span>
          </div>
        </div>

        <div class="form-actions">
          <el-button @click="closeManagerPopup">取消</el-button>
          <el-button type="primary" @click="confirmManager" :loading="managerLoading">确认指派</el-button>
        </div>
      </div>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import { getDicItemList } from "@/config/tool.js";
import { getLegalList } from "@/api/processManagement";
import { rootPath } from "@/utils/globalParam";
import { getBranchUsers } from "@/api/processManagement";
import { closeOpportunity, acceptTask, resumeOpportunity, suspendOpportunity, setBranchCoordination, setOpportunityProjecter } from "@/api/workbench";
import { countUserParticipatedOpportunities, restartOpportunity, getCloseReason, getOpportunityTimeline, getOpportunityProgressLog, getOpportunityProcessProgress, getOpportunityDetailFull, completeTask, findLegalOrgDataByTenantId } from "@/api/workbench";
import {
  kbcPath
} from "@/utils/globalParam";
export default {
  name: "baseInfo",
  inject: ['opportunityDetailFn', 'opportunityFn', 'enterpriseFn', 'opportunityProcessLogFn'],
  data() {
    return {
      // 机会状态：normal-正常推进，paused-暂停状态，closed-已关闭状态
      opportunityStatus: "normal", // 默认正常状态
      active: 9,
      processList: [
        {
          name: "统筹跟进",
          status: "0"
        },
        {
          name: "立项组队",
          status: "0"
        },
        {
          name: "分配比例",
          status: "0"
        },
        {
          name: "投标阶段",
          status: "0"
        },
        {
          name: "客户授权",
          status: "0"
        },
        {
          name: "询价阶段",
          status: "0"
        },
        {
          name: "排分阶段",
          status: "0"
        },
        {
          name: "成交出单",
          status: "0"
        },
        {
          name: "服务阶段",
          status: "0"
        }
      ],
      tableData: [],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          agentNo: "",
          kbaoNo: "",
          stuName: "",
          examId: ""
        }
      },
      searchFormTemp: [
        {
          lable: "登录用户姓名",
          name: "stuName",
          type: "input",
          width: "200px"
        },
        {
          lable: "工号",
          name: "agentNo",
          type: "input",
          width: "200px"
        },
        {
          lable: "快保账号",
          name: "kbaoNo",
          type: "input",
          width: "200px"
        }
      ],
      total: 0,
      exportUrl: "",
      paramObj: {
        agentNo: "",
        kbaoNo: "",
        stuName: ""
      },

      // 领取机会相关
      showClaimPopup: false,
      claimLoading: false,

      // 暂停机会相关
      showPausePopup: false,
      pauseForm: {
        reason: ""
      },
      pauseRules: {
        reason: [
          { required: true, message: "请输入暂停原因", trigger: "blur" },
          { min: 1, max: 150, message: "长度在 1 到 150 个字符", trigger: "blur" }
        ]
      },
      pauseLoading: false,

      // 启动机会相关
      showRestartPopup: false,
      restartLoading: false,

      // 关闭机会相关
      showClosePopup: false,
      closeForm: {
        closeReasonType: "",
        closeReason: ""
      },
      closeRules: {
        closeReasonType: [
          { required: true, message: "请选择关闭原因", trigger: "change" }
        ]
      },
      closeLoading: false,

      // 重启关闭机会相关
      showRestartClosePopup: false,
      restartCloseBusinessType: "T0001", // 默认选择经纪
      closeReasonInfo: null, // 存储关闭原因信息
      restartCloseSearchForm: {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleType: '3', // 默认选择分公司项目经理
          organCode: "",
          nickName: ''
        }
      },
      restartCloseSearchFormTemp: [
        { label: '指派对象', name: 'roleType', clearable: false, type: 'select', width: '140px', list: [{ dicItemCode: '3', dicItemName: '分公司项目经理' }, { dicItemCode: '4', dicItemName: '总公司项目经理' }, { dicItemCode: '7', dicItemName: '营业部内勤' }] },
        { label: '机构', name: 'organCode', clearable: false, type: 'select', width: '140px', list: [] },
        { label: '人员姓名', name: 'nickName', type: 'input', width: '140px' }
      ],
      restartCloseUserList: [],
      selectedRestartCloseUserId: "",
      restartCloseLoading: false,
      selectedRestartCloseUserInfo: null, // 新增：用于存储选中人员的信息
      restartCloseForm: {
        reasonDesc: ""
      },
      restartCloseRules: {
        reasonDesc: [
          { required: true, message: "请输入重启原因", trigger: "blur" },
          { min: 1, max: 150, message: "长度在 1 到 150 个字符", trigger: "blur" }
        ]
      },

      // 指派统筹相关
      showAssignPopup: false,
      assignSearchForm: {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleType: '1',
          organCode: "",
          nickName: '',

        }
      },
      assignSearchFormTemp: [
        { label: '指派对象', name: 'roleType', clearable: false, type: 'select', width: '140px', list: [{ dicItemCode: '1', dicItemName: '分公司统筹' }, { dicItemCode: '2', dicItemName: '总公司统筹' }] },
        { label: '机构', name: 'organCode', clearable: false, type: 'select', width: '140px', list: [] },
        { label: '人员姓名', name: 'nickName', type: 'input', width: '140px' }
      ],
      assignUserList: [
      ],
      selectedAssignUserId: "",
      assignLoading: false,
      selectedUserInfo: null, // 新增：用于存储选中人员的信息

      // 指派项目经理相关
      showManagerPopup: false,
      managerBusinessType: "T0001", // 默认选择经纪
      managerSearchForm: {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleType: '3', // 默认选择分公司项目经理
          organCode: "",
          nickName: '',
        }
      },
      managerSearchFormTemp: [
        { label: '指派对象', name: 'roleType', clearable: false, type: 'select', width: '140px', list: [{ dicItemCode: '3', dicItemName: '分公司项目经理' }, { dicItemCode: '4', dicItemName: '总公司项目经理' }, { dicItemCode: '7', dicItemName: '营业部内勤' }] },
        { label: '机构', name: 'organCode', clearable: false, type: 'select', width: '140px', list: [] },
        { label: '人员姓名', name: 'nickName', type: 'input', width: '140px' }
      ],
      managerUserList: [],
      selectedManagerUserId: "",
      managerLoading: false,
      selectedManagerUserInfo: null, // 新增：用于存储选中人员的信息

      // 任务完成状态
      taskCompletionStatus: {},
      // 任务加载状态
      taskLoadingStatus: {},

      // 机会完整详情
      opportunityDetail: {
        enterprise: {}, // 客户信息
        opportunity: {}, // 机会基本信息
        opportunityProcessLog: {}, // 机会详细信息
      },

      // 步骤列表数据 - 使用datas结构
      stepList: [],

      // 缓存接口数据
      cachedLegalOrgData: {},

      "datas": [
        {
          "activityName": "", //stepList.tasks.title
          "description": "", // stepList.title
          "customProperties": {
            "dolist": [ //stepList.tasks.actions
                    {
                      "id": "", //stepList.tasks.actions.id
                      "name": "", //stepList.tasks.actions.name
                      "required": true //stepList.tasks.actions.required
                    }
                  ],
          },
          "status": "",  //状态：1-已完成，0-进行中，-1-待执行
          "timelineTime": "", // stepList.date
          "title": "", // 标题 该字段暂不现实
          "type": ""  //类型：PROGRESS-流程进度，LOG-操作日志
        }
      ],
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup

  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    // 获取企客用户权限信息
    qikeUserInfo() {
      return this.$store.getters["layoutStore/getQikeUserInfo"];
    },
    // 从父组件获取注入的数据
    injectedOpportunityDetail() {
      return this.opportunityDetailFn ? this.opportunityDetailFn() : null;
    },
    injectedOpportunity() {
      return this.opportunityFn ? this.opportunityFn() : null;
    },
    injectedEnterprise() {
      return this.enterpriseFn ? this.enterpriseFn() : null;
    },
    injectedOpportunityProcessLog() {
      return this.opportunityProcessLogFn ? this.opportunityProcessLogFn() : null;
    },

    closeReasonRules() {
      // 当选择"机会已成交"时，关闭原因不是必填
      if (this.closeForm.closeReasonType === '1') {
        return [
          { min: 0, max: 150, message: "长度不能超过 150 个字符", trigger: "blur" }
        ];
      }
      // 其他选项时，关闭原因是必填的
      return [
        { required: true, message: "请输入关闭原因", trigger: "blur" },
        { min: 1, max: 150, message: "长度在 1 到 150 个字符", trigger: "blur" }
      ];
    },
    closeReasonPlaceholder() {
      // 当选择"机会已成交"时，关闭原因不是必填
      if (this.closeForm.closeReasonType === '1') {
        return "关闭原因 (选填)";
      }
      // 其他选项时，关闭原因是必填的
      return "关闭原因 (必填)";
    },
    toolList() {
      const status = (this.opportunityDetail.opportunity && this.opportunityDetail.opportunity.status);
      const coordinator = (this.opportunityDetail.opportunity && this.opportunityDetail.opportunity.coordinator);
      const projectManager = (this.opportunityDetail.opportunity && this.opportunityDetail.opportunity.projectManager);

      const buttons = [{name:"平安询价"}];

      // status为0时，所有按钮都不展示
      if (status == 0||this.$route.query.title=="机会管理") {
        return buttons;
      }

      // coordinator有值显示领取机会 - 需要权限：elms:opportunity:receive
      if (!coordinator && this.hasPermission('elms:opportunity:receive')) {
        buttons.push({ name: "领取机会" });
      }

      // projectManager无值显示指派项目经理、指派统筹，有值则都不显示
      if (!projectManager&&coordinator&&coordinator==this.$store.state.layoutStore.currentLoginUser.userId) {
        buttons.push({ name: "指派统筹" });
        buttons.push({ name: "指派项目经理" });
      }

      // 根据status显示不同的机会操作按钮
      if (status == 1) {
        // 关闭机会 - 需要权限：elms:opportunity:close
        if (this.hasPermission('elms:opportunity:close')) {
          buttons.push({ name: "关闭机会" });
        }
        // 暂停机会 - 需要权限：elms:opportunity:stop
        if (this.hasPermission('elms:opportunity:stop')) {
          buttons.push({ name: "暂停机会"});
        }
      } else if (status == 3) {
        // 启动机会 - 需要权限：elms:opportunity:restart:pause 暂停重启
        if (this.hasPermission('elms:opportunity:restart:pause')) {
          buttons.push({ name: "启动机会" });
        }
        // 关闭机会 - 需要权限：elms:opportunity:close
        if (this.hasPermission('elms:opportunity:close')) {
          buttons.push({ name: "关闭机会" });
        }
      } else if (status == 4) {
        // 重启机会 - 需要权限：elms:opportunity:restart:fail 推进失败重启
        if (this.hasPermission('elms:opportunity:restart:fail')) {
          buttons.push({ name: "重启机会"});
        }
      }

      return buttons;
    }
  },
  watch: {
    'closeForm.closeReasonType'() {
      // 当关闭原因类型改变时，重新验证关闭原因字段
      this.$nextTick(() => {
        if (this.$refs.closeFormRef) {
          this.$refs.closeFormRef.validateField('closeReason');
        }
      });
    },
    // 监听父组件注入的数据变化
    injectedOpportunityDetail: {
      handler(newVal) {
        if (newVal && newVal.opportunity && newVal.opportunity.id) {
          this.opportunityDetail = newVal;
        }
      },
      deep: true
    }
  },
  async created() {
    // 从父组件注入的数据中获取机会详情
    const injectedOpportunityDetail = this.injectedOpportunityDetail;
    if (injectedOpportunityDetail && injectedOpportunityDetail.opportunity && injectedOpportunityDetail.opportunity.id) {
      this.opportunityDetail = injectedOpportunityDetail;
      // 如果父组件已有数据，只获取流程相关数据
      await this.getOpportunityProcessProgressFun();
      await this.getStepListData();
    } else {
      // 如果父组件还没有数据，则自己获取
      await this.initData();
    }
  },

  beforeDestroy() {
    // 清理缓存数据
    this.cachedLegalOrgData = {};
  },
  methods: {
    handleViewReport(url){
      window.open(url,'_blank');
    },
    handleViewQichachaReport(){
      if (window.parent && window.parent.kbcHopRouting) {
        const creditCode = this.opportunityDetail.enterprise.creditCode;
        const name = this.opportunityDetail.enterprise.name;
        const obj = {
          rootPath:"kbc-elms",
          routePath:"enterprise/info",
          customFuncName:name+"-信息详情",
          postChildUrl:`iframe?iframeUrl=${kbcPath}kbc-elms/enterprise/third-party-query/${creditCode}?creditCode=${creditCode}`,
          customId:creditCode,
        }
        window.parent.kbcHopRouting(obj);
      }

    },
    // 检查当前节点是否是第一个status等于0的节点
    isFirstStatusZeroNode(item, index) {
      // 如果当前节点的status不等于0，直接返回false
      if (item.status !== '0') {
        return false;
      }
      if (this.opportunityDetail.opportunity.status != 1) {
        return false;
      }

      // 检查是否是stepList中第一个status为'0'的项目
      for (let i = 0; i < this.stepList.length; i++) {
        if (this.stepList[i].status === '0') {
          return i === index;
        }
      }
      return false;
    },

    // 判断是否应该显示确认完成按钮
    shouldShowTaskCompletionButton(item, index) {
      // 机会管理过来的不能点击
      if(this.$route.query.title=="机会管理"){
        return false
      }


      // 如果机会状态不是1，不显示按钮
      if (this.opportunityDetail.opportunity.status != 1) {
        return false;
      }
      
      // 找到当前步骤（第一个status为'0'的步骤）
      let currentStepIndex = -1;
      for (let i = 0; i < this.stepList.length; i++) {
        if (this.stepList[i].status === '0') {
          currentStepIndex = i;
          break;
        }
      }
      
      // 如果没有找到当前步骤，不显示按钮
      if (currentStepIndex === -1) {
        return false;
      }
      
      // 只显示当前步骤及以后的步骤的按钮
      return index >= currentStepIndex;
    },

    // 判断是否是当前步骤（可点击的步骤）
    isCurrentStep(item, index) {
      // 如果机会状态不是1，不是当前步骤
      if (this.opportunityDetail.opportunity.status != 1) {
        return false;
      }
      
      // 如果当前节点的status不等于0，不是当前步骤
      if (item.status !== '0') {
        return false;
      }
      
      // 检查是否是stepList中第一个status为'0'的项目
      for (let i = 0; i < this.stepList.length; i++) {
        if (this.stepList[i].status === '0') {
          return i === index;
        }
      }
      return false;
    },

    // 检查customProperties.dolist中是否包含"指派项目经理"
    hasAssignProjectManager(item) {
      if (!item.customProperties || !item.customProperties.dolist) {
        return false;
      }
      return item.customProperties.dolist.some(action => action.id == '16');
    },

    // 检查用户是否有指定权限
    hasPermission() {
      return (permissionCode) => {
        if (!this.qikeUserInfo || !this.qikeUserInfo.roleAuthStr) {
          return false;
        }
        const roleAuthStr = this.qikeUserInfo.roleAuthStr;
        const permissions = roleAuthStr.split(',').map(item => item.trim());
        return permissions.includes(permissionCode);
      };
    },

    // 初始化任务状态
    initTaskStatus() {
      this.stepList.forEach(item => {
        if (item.type == 'PROGRESS' && item.customProperties && item.customProperties.dolist) {
          item.customProperties.dolist.forEach(action => {
            this.taskCompletionStatus[action.name] = false;
            this.taskLoadingStatus[action.name] = false;
          });
        }
      });
    },
    async initData() {
      await this.getOpportunityDetailFullFun();
      await this.getOpportunityProcessProgressFun();
      await this.getStepListData();
    },
    async getOpportunityProcessProgressFun() {
      let res = await getOpportunityProcessProgress({
        opportunityId: this.$route.query.id
      });
      if (res) {
        // 根据返回的数据更新processList
        this.processList = res.map(item => ({
          name: item.activityName || '',
          status: item.status || '0'
        }));

        // 计算当前激活的步骤索引 - 取最后一个status为"1"的索引
        const filteredItems = res.map((item, index) => ({ ...item, originalIndex: index }))
          .filter(item => item.status === '1');
        const lastItem = filteredItems.pop();
        const activeIndex = lastItem ? lastItem.originalIndex : undefined;
        this.active = activeIndex !== undefined ? activeIndex + 1 : 0;
      }
    },

    async getOpportunityDetailFullFun() {
      let res = await getOpportunityDetailFull({
        opportunityId: this.$route.query.id
      });
      if (res) {
        this.opportunityDetail = res;
        
        // 如果父组件存在，更新父组件的数据
        if (this.$parent && this.$parent.updateOpportunityDetail) {
          this.$parent.updateOpportunityDetail(res);
        }
        
        // 根据接口返回的状态更新机会状态
        if (res.opportunity && res.opportunity.status !== undefined) {
          const status = res.opportunity.status;
          // 根据新的状态逻辑更新opportunityStatus
          if (status == 0) {
            this.opportunityStatus = 'normal'; // status为0时，设置为normal状态
          } else if (status == 1) {
            this.opportunityStatus = 'normal'; // status为1时，正常状态，可暂停和关闭
          } else if (status == 3) {
            this.opportunityStatus = 'paused'; // status为3时，暂停状态，可启动和关闭
          } else if (status == 4) {
            this.opportunityStatus = 'closed'; // status为4时，关闭状态，可重启
          } else {
            this.opportunityStatus = 'normal'; // 默认状态
          }
        }
      }
    },

    // 获取步骤列表数据
    async getStepListData() {
      const currentUser = this.$store.getters["layoutStore/getCurrentLoginUser"];
      const currentUserId = currentUser && currentUser.userId;
      const projectManager = (this.opportunityDetail.opportunity && this.opportunityDetail.opportunity.projectManager);

      try {
        let res;
        if (projectManager == currentUserId) {
          // 如果项目经理等于当前用户，调用getOpportunityTimeline接口
          res = await getOpportunityTimeline({
            opportunityId: this.$route.query.id
          });
        } else {
          // 否则调用getOpportunityProgressLog接口
          res = await getOpportunityProgressLog({
            opportunityId: this.$route.query.id
          });
        }

        if (res) {
          this.stepList = res;
          this.initTaskStatus();
        }
      } catch (error) {
        this.stepList = [];
      }
    },
    async handleGetLegalListFun(tenantIdParam) {
      const tenantId = tenantIdParam || "T0001";

      // 检查缓存中是否已有数据
      if (!this.cachedLegalOrgData[tenantId]) {
        try {
          const res = await findLegalOrgDataByTenantId({ tenantId });
          if (res) {
            this.cachedLegalOrgData[tenantId] = res;
          } else {
            // 接口返回无数据时，清空相关的机构列表
            this.clearOrgListsByTenantId(tenantIdParam);
            return;
          }
        } catch (error) {
          // 接口调用失败时，清空相关的机构列表
          this.clearOrgListsByTenantId(tenantIdParam);
          return;
        }
      }

      const res = this.cachedLegalOrgData[tenantId];

      // 根据orgType判断是否为总公司
      const isHeadquarters = res.orgType === 'dept';

      // 根据不同的业务场景更新对应的指派对象选项
      if (!tenantIdParam) {
        // 指派统筹场景
        // 根据orgType动态调整指派对象选项
        if (isHeadquarters) {
          // 总公司场景：包含总公司统筹
          this.assignSearchFormTemp[0].list = [
            { dicItemCode: '1', dicItemName: '分公司统筹' },
            { dicItemCode: '2', dicItemName: '总公司统筹' }
          ];
        } else {
          // 分公司场景：只显示分公司统筹
          this.assignSearchFormTemp[0].list = [{ dicItemCode: '1', dicItemName: '分公司统筹' }];
        }
        // 机构列表根据当前选择的指派对象来决定
        this.updateOrgListByRoleType(this.assignSearchForm.param.roleType, this.assignSearchFormTemp, res);
      } else if (tenantIdParam === this.managerBusinessType) {
        // 指派项目经理场景
        // 根据orgType动态调整指派对象选项
        if (isHeadquarters) {
          // 总公司场景：包含总公司项目经理
          this.managerSearchFormTemp[0].list = [
            { dicItemCode: '3', dicItemName: '分公司项目经理' },
            { dicItemCode: '4', dicItemName: '总公司项目经理' },
            { dicItemCode: '7', dicItemName: '营业部内勤' }
          ];
        } else {
          // 分公司场景：显示分公司项目经理和营业部内勤
          this.managerSearchFormTemp[0].list = [
            { dicItemCode: '3', dicItemName: '分公司项目经理' },
            { dicItemCode: '7', dicItemName: '营业部内勤' }
          ];
        }
        // 机构列表根据当前选择的指派对象来决定
        this.updateOrgListByRoleType(this.managerSearchForm.param.roleType, this.managerSearchFormTemp, res);
      } else if (tenantIdParam === this.restartCloseBusinessType) {
        // 重启关闭机会场景
        // 根据orgType动态调整指派对象选项
        if (isHeadquarters) {
          // 总公司场景：包含总公司项目经理
          this.restartCloseSearchFormTemp[0].list = [
            { dicItemCode: '3', dicItemName: '分公司项目经理' },
            { dicItemCode: '4', dicItemName: '总公司项目经理' },
            { dicItemCode: '7', dicItemName: '营业部内勤' }
          ];
        } else {
          // 分公司场景：显示分公司项目经理和营业部内勤
          this.restartCloseSearchFormTemp[0].list = [
            { dicItemCode: '3', dicItemName: '分公司项目经理' },
            { dicItemCode: '7', dicItemName: '营业部内勤' }
          ];
        }
        // 机构列表根据当前选择的指派对象来决定
        this.updateOrgListByRoleType(this.restartCloseSearchForm.param.roleType, this.restartCloseSearchFormTemp, res);
      }
    },

    // 清空相关的机构列表数据
    clearOrgListsByTenantId(tenantIdParam) {
      if (!tenantIdParam) {
        // 指派统筹场景 - 清空机构列表
        this.assignSearchFormTemp[1].list = [];
        // 重置表单数据
        this.assignSearchForm.param.organCode = "";
      } else if (tenantIdParam === this.managerBusinessType) {
        // 指派项目经理场景 - 清空机构列表和人员
        this.managerSearchFormTemp[1].list = [];
        // 重置表单数据
        this.managerSearchForm.param.organCode = "";
        this.managerSearchForm.param.nickName = "";
      } else if (tenantIdParam === this.restartCloseBusinessType) {
        // 重启关闭机会场景 - 清空机构列表和人员
        this.restartCloseSearchFormTemp[1].list = [];
        // 重置表单数据
        this.restartCloseSearchForm.param.organCode = "";
        this.restartCloseSearchForm.param.nickName = "";
      }
    },

    // 根据指派对象类型更新机构列表
    updateOrgListByRoleType(roleType, searchFormTemp, res) {
      // 判断是否为总公司相关选项
      const isHeadquartersRole = roleType === '2' || roleType === '4'; // 2:总公司统筹, 4:总公司项目经理

      if (isHeadquartersRole) {
        // 选择总公司相关选项时，机构列表固定为总公司
        searchFormTemp[1].list = [{ dicItemCode: "", dicItemName: "总公司" }];
      } else {
        // 选择其他选项时，使用接口返回的orgList
        const orgList = res.orgList ? res.orgList.map(item => ({ dicItemCode: item.orgCode, dicItemName: item.orgName })) : [];
        searchFormTemp[1].list = orgList;
      }
    },

    // 处理指派对象变化时的机构列表更新
    async handleRoleTypeChange(roleType, searchFormTemp, searchForm) {
      // 判断是否为总公司相关选项
      const isHeadquartersRole = roleType === '2' || roleType === '4'; // 2:总公司统筹, 4:总公司项目经理

      if (isHeadquartersRole) {
        // 选择总公司相关选项时，机构列表固定为总公司
        searchFormTemp[1].list = [{ dicItemCode: "", dicItemName: "总公司" }];
        searchForm.param.organCode = "";
      } else {
        // 选择其他选项时，使用缓存数据更新机构列表
        // 根据不同的搜索表单确定对应的业务类型
        let tenantIdParam = null;
        if (searchFormTemp === this.assignSearchFormTemp) {
          tenantIdParam = null; // 指派统筹场景
        } else if (searchFormTemp === this.managerSearchFormTemp) {
          tenantIdParam = this.managerBusinessType;
        } else if (searchFormTemp === this.restartCloseSearchFormTemp) {
          tenantIdParam = this.restartCloseBusinessType;
        }

        const tenantId = tenantIdParam || "T0001";

        // 检查缓存中是否已有数据
        if (!this.cachedLegalOrgData[tenantId]) {
          const res = await findLegalOrgDataByTenantId({ tenantId });
          if (res) {
            this.cachedLegalOrgData[tenantId] = res;
          } else {
            return;
          }
        }

        const res = this.cachedLegalOrgData[tenantId];
        this.updateOrgListByRoleType(roleType, searchFormTemp, res);
        // 更新机构选择为第一个可用选项

        if (searchFormTemp[1].list && searchFormTemp[1].list.length > 0) {
          searchForm.param.organCode = searchFormTemp[1].list[0].dicItemCode;
        }
      }

      // 清空之前选中的用户状态
      if (searchFormTemp === this.assignSearchFormTemp) {
        this.selectedAssignUserId = "";
        this.selectedUserInfo = null;
      } else if (searchFormTemp === this.managerSearchFormTemp) {
        this.selectedManagerUserId = "";
        this.selectedManagerUserInfo = null;
      } else if (searchFormTemp === this.restartCloseSearchFormTemp) {
        this.selectedRestartCloseUserId = "";
        this.selectedRestartCloseUserInfo = null;
      }
    },

    handleTool(item) {
      let m = {
        "领取机会": this.handleReceive,
        "指派统筹": this.handleOverallPlanning,
        "指派项目经理": this.handleProjectManager,
        "暂停机会": this.handlePause,
        "启动机会": this.handleRestart,
        "关闭机会": this.handleClose,
        "重启机会": this.handleRestartClose,
        "平安询价":this.handleInquiryConfig
      }
      const handler = m[item.name];
      if (handler) {
        return handler();
      }
    },
    handleInquiryConfig(){
      this.$router.push({
        name: "inquiryConfig",
        query: {
          opportunityId: this.$route.query.id
        }
      });
    },
    handleReceive() {
      this.showClaimPopup = true;
    },
    async handleOverallPlanning() {
      await this.handleGetLegalListFun();
      // 设置默认的指派对象为第一个选项（通常是分公司统筹）
      if (this.assignSearchFormTemp[0].list && this.assignSearchFormTemp[0].list.length > 0) {
        this.assignSearchForm.param.roleType = this.assignSearchFormTemp[0].list[0].dicItemCode;
      }
      // 根据默认的指派对象设置机构列表
      const tenantId = "T0001";
      if (this.cachedLegalOrgData[tenantId]) {
        this.updateOrgListByRoleType(this.assignSearchForm.param.roleType, this.assignSearchFormTemp, this.cachedLegalOrgData[tenantId]);
        // 设置默认的机构为第一个选项
        if (this.assignSearchFormTemp[1].list && this.assignSearchFormTemp[1].list.length > 0) {
          this.assignSearchForm.param.organCode = this.assignSearchFormTemp[1].list[0].dicItemCode;
        }
      }
      this.assignSearchForm.param.nickName = ""
      this.showAssignPopup = true;
      this.handleAssignSearchFun()
    },
    handleAssignSearch(item, data) {
      this.assignSearchForm = data;

      // 如果是指派对象变化，需要更新机构列表
      if (item && item.name === 'roleType') {
        this.handleRoleTypeChange(data.param.roleType, this.assignSearchFormTemp, this.assignSearchForm);
      }

      this.handleAssignSearchFun();
    },
    async handleAssignUserSelect(row) {
      if (!row) {
        this.selectedUserInfo = null;
        return;
      }

      try {
        // 调用接口获取参与机会数和待完成任务数
        const res = await countUserParticipatedOpportunities({
          userId: row.userId
        });

        if (res) {
          this.selectedUserInfo = {
            ...row,
            opportunityCount: res.opportunityCount || 0,
            taskCount: res.taskCount || 0
          };
        } else {
          // 如果接口调用失败，使用默认值
          this.selectedUserInfo = {
            ...row,
            opportunityCount: 0,
            taskCount: 0
          };
        }
      } catch (error) {
        // 接口调用失败时使用默认值
        this.selectedUserInfo = {
          ...row,
          opportunityCount: 0,
          taskCount: 0
        };
      }
    },
    async handleProjectManager() {
      await this.handleGetLegalListFun(this.managerBusinessType);
      // 设置默认的指派对象为第一个选项（通常是分公司项目经理）
      if (this.managerSearchFormTemp[0].list && this.managerSearchFormTemp[0].list.length > 0) {
        this.managerSearchForm.param.roleType = this.managerSearchFormTemp[0].list[0].dicItemCode;
      }
      // 根据默认的指派对象设置机构列表
      const tenantId = this.managerBusinessType;
      if (this.cachedLegalOrgData[tenantId]) {
        this.updateOrgListByRoleType(this.managerSearchForm.param.roleType, this.managerSearchFormTemp, this.cachedLegalOrgData[tenantId]);
        // 设置默认的机构为第一个选项
        if (this.managerSearchFormTemp[1].list && this.managerSearchFormTemp[1].list.length > 0) {
          this.managerSearchForm.param.organCode = this.opportunityDetail.opportunity.legalCode||this.managerSearchFormTemp[1].list[0].dicItemCode;
        }
      }
      this.managerSearchForm.param.nickName = ""
      this.showManagerPopup = true;
      this.handleManagerSearchFun()
    },
    handleManagerSearch(item, data) {
      this.managerSearchForm = data;

      // 如果是指派对象变化，需要更新机构列表
      if (item && item.name === 'roleType') {
        this.handleRoleTypeChange(data.param.roleType, this.managerSearchFormTemp, this.managerSearchForm);
      }

      this.handleManagerSearchFun();
    },
    async handleManagerSearchFun() {
      this.managerSearchForm.param.tenantId = this.managerBusinessType
      let res = await getBranchUsers(this.managerSearchForm.param);
      if (res && _.isArray(res)) {
        this.managerUserList = res;
      } else {
        this.managerUserList = [];
      }
      // 清空之前选中的用户状态
      this.selectedManagerUserId = "";
      this.selectedManagerUserInfo = null;
    },
    async handleManagerUserSelect(row) {
      if (!row) {
        this.selectedManagerUserInfo = null;
        return;
      }

      try {
        // 调用接口获取参与机会数和待完成任务数
        const res = await countUserParticipatedOpportunities({
          userId: row.userId
        });

        if (res) {
          this.selectedManagerUserInfo = {
            ...row,
            opportunityCount: res.opportunityCount || 0,
            taskCount: res.taskCount || 0
          };
        } else {
          // 如果接口调用失败，使用默认值
          this.selectedManagerUserInfo = {
            ...row,
            opportunityCount: 0,
            taskCount: 0
          };
        }
      } catch (error) {
        // 接口调用失败时使用默认值
        this.selectedManagerUserInfo = {
          ...row,
          opportunityCount: 0,
          taskCount: 0
        };
      }
    },
    // 业务主体变化处理
    async handleManagerBusinessTypeChange(value) {
      // 重新查询机构
      await this.handleGetLegalListFun(value);
      // 清空人员姓名
      this.managerSearchForm.param.nickName = "";
      // 重置指派对象选择为第一个选项
      if (this.managerSearchFormTemp[0].list && this.managerSearchFormTemp[0].list.length > 0) {
        this.managerSearchForm.param.roleType = this.managerSearchFormTemp[0].list[0].dicItemCode;
      }
      // 根据新的指派对象设置机构列表
      if (this.cachedLegalOrgData[value]) {
        this.updateOrgListByRoleType(this.managerSearchForm.param.roleType, this.managerSearchFormTemp, this.cachedLegalOrgData[value]);
        // 重置机构选择为第一个选项
        if (this.managerSearchFormTemp[1].list && this.managerSearchFormTemp[1].list.length > 0) {
          this.managerSearchForm.param.organCode = this.managerSearchFormTemp[1].list[0].dicItemCode;
        }
      }
      // 清空之前选中的用户状态
      this.selectedManagerUserId = "";
      this.selectedManagerUserInfo = null;
      // 重新调用查询用户
      this.handleManagerSearchFun();
    },
    handlePause() {
      this.showPausePopup = true;
    },
    handleRestart() {
      this.showRestartPopup = true;
    },
    handleClose() {
      this.showClosePopup = true;
    },

    // 领取机会确认
    async confirmClaim() {
      try {
        this.claimLoading = true;
        let res = await acceptTask({
          opportunityId: this.$route.query.id,
        });
        if (res) {
          this.$message.success("机会领取成功");
          this.closeClaimPopup();
          this.initData();
        }
      } catch (error) {
      } finally {
        this.claimLoading = false;
      }
    },

    // 暂停机会确认
    async confirmPause() {
      try {
        await this.$refs.pauseFormRef.validate();
        this.pauseLoading = true;
        let res = await suspendOpportunity({
          opportunityId: this.$route.query.id,
          reason: this.pauseForm.reason
        });
        if (res) {
          this.opportunityStatus = "paused"; // 改变状态为暂停
          this.$message.success("机会暂停成功");
          this.closePausePopup();
          this.initData();
        }
      } catch (error) {
      } finally {
        this.pauseLoading = false;
      }
    },

    // 关闭机会确认
    async confirmClose() {
      try {
        await this.$refs.closeFormRef.validate();
        this.closeLoading = true;
        let res = await closeOpportunity({
          opportunityId: this.$route.query.id,
          reasonDesc: this.closeForm.closeReason,
          reasonType: this.closeForm.closeReasonType
        });
        if (res) {
          this.opportunityStatus = "closed"; // 改变状态为已关闭
          this.$message.success("机会关闭成功");
          this.closeClosePopup();
          this.initData();
        }
      } catch (error) {
      } finally {
        this.closeLoading = false;
      }
    },

    // 关闭领取弹窗
    closeClaimPopup() {
      this.showClaimPopup = false;
    },

    // 关闭暂停弹窗
    closePausePopup() {
      this.showPausePopup = false;
      this.pauseForm.reason = "";
      this.$nextTick(() => {
        if (this.$refs.pauseFormRef) {
          this.$refs.pauseFormRef.resetFields();
        }
      });
    },

    // 启动机会确认
    async confirmRestart() {
      try {
        this.restartLoading = true;
        let res = await resumeOpportunity({
          opportunityId: this.$route.query.id,
        });
        if (res) {
          this.opportunityStatus = "normal"; // 改变状态为正常
          this.$message.success("机会重启成功");
          this.closeRestartPopup();
          this.initData();
        }
      } catch (error) {
      } finally {
        this.restartLoading = false;
      }
    },

    closeRestartPopup() {
      this.showRestartPopup = false;
    },

    // 重启关闭机会
    async handleRestartClose() {
      await this.handleGetLegalListFun(this.restartCloseBusinessType);
      // 设置默认的指派对象为第一个选项（通常是分公司项目经理）
      if (this.restartCloseSearchFormTemp[0].list && this.restartCloseSearchFormTemp[0].list.length > 0) {
        this.restartCloseSearchForm.param.roleType = this.restartCloseSearchFormTemp[0].list[0].dicItemCode;
      }
      // 根据默认的指派对象设置机构列表
      const tenantId = this.restartCloseBusinessType;
      if (this.cachedLegalOrgData[tenantId]) {
        this.updateOrgListByRoleType(this.restartCloseSearchForm.param.roleType, this.restartCloseSearchFormTemp, this.cachedLegalOrgData[tenantId]);
        // 设置默认的机构为第一个选项
        if (this.restartCloseSearchFormTemp[1].list && this.restartCloseSearchFormTemp[1].list.length > 0) {
          this.restartCloseSearchForm.param.organCode = this.restartCloseSearchFormTemp[1].list[0].dicItemCode;
        }
      }
      this.restartCloseSearchForm.param.nickName = ""
      this.showRestartClosePopup = true;
      this.handleRestartCloseSearchFun()

      // 获取关闭原因信息
      await this.getCloseReasonInfo()
    },

    // 重启关闭机会搜索
    handleRestartCloseSearch(item, data) {
      this.restartCloseSearchForm = data;

      // 如果是指派对象变化，需要更新机构列表
      if (item && item.name === 'roleType') {
        this.handleRoleTypeChange(data.param.roleType, this.restartCloseSearchFormTemp, this.restartCloseSearchForm);
      }

      this.handleRestartCloseSearchFun();
    },

    async handleRestartCloseSearchFun() {
      this.restartCloseSearchForm.param.tenantId = this.restartCloseBusinessType
      let res = await getBranchUsers(this.restartCloseSearchForm.param);
      if (res && _.isArray(res)) {
        this.restartCloseUserList = res;
      } else {
        this.restartCloseUserList = [];
      }
      // 清空之前选中的用户状态
      this.selectedRestartCloseUserId = "";
      this.selectedRestartCloseUserInfo = null;
    },

    async handleRestartCloseUserSelect(row) {
      if (!row) {
        this.selectedRestartCloseUserInfo = null;
        return;
      }

      try {
        // 调用接口获取参与机会数和待完成任务数
        const res = await countUserParticipatedOpportunities({
          userId: row.userId
        });

        if (res) {
          this.selectedRestartCloseUserInfo = {
            ...row,
            opportunityCount: res.opportunityCount || 0,
            taskCount: res.taskCount || 0
          };
        } else {
          // 如果接口调用失败，使用默认值
          this.selectedRestartCloseUserInfo = {
            ...row,
            opportunityCount: 0,
            taskCount: 0
          };
        }
      } catch (error) {
        // 接口调用失败时使用默认值
        this.selectedRestartCloseUserInfo = {
          ...row,
          opportunityCount: 0,
          taskCount: 0
        };
      }
    },

    // 业务主体变化处理
    async handleRestartCloseBusinessTypeChange(value) {
      // 重新查询机构
      await this.handleGetLegalListFun(value);
      // 清空人员姓名
      this.restartCloseSearchForm.param.nickName = "";
      // 重置指派对象选择为第一个选项
      if (this.restartCloseSearchFormTemp[0].list && this.restartCloseSearchFormTemp[0].list.length > 0) {
        this.restartCloseSearchForm.param.roleType = this.restartCloseSearchFormTemp[0].list[0].dicItemCode;
      }
      // 根据新的指派对象设置机构列表
      if (this.cachedLegalOrgData[value]) {
        this.updateOrgListByRoleType(this.restartCloseSearchForm.param.roleType, this.restartCloseSearchFormTemp, this.cachedLegalOrgData[value]);
        // 重置机构选择为第一个选项
        if (this.restartCloseSearchFormTemp[1].list && this.restartCloseSearchFormTemp[1].list.length > 0) {
          this.restartCloseSearchForm.param.organCode = this.restartCloseSearchFormTemp[1].list[0].dicItemCode;
        }
      }
      // 清空之前选中的用户状态
      this.selectedRestartCloseUserId = "";
      this.selectedRestartCloseUserInfo = null;
      // 重新调用查询用户
      this.handleRestartCloseSearchFun();
    },

    async confirmRestartClose() {
      if (!this.selectedRestartCloseUserId) {
        this.$message.warning("请选择指派人员");
        return;
      }

      try {
        await this.$refs.restartCloseFormRef.validate();
        this.restartCloseLoading = true;
        let res = await restartOpportunity({
          opportunityId: this.$route.query.id,
          assignee: this.selectedRestartCloseUserId,
          assigneeRoleType: this.restartCloseSearchForm.param.roleType,
          assigneeOrg: this.restartCloseSearchForm.param.organCode,
          businessTenantId: this.restartCloseBusinessType,
          reasonDesc: this.restartCloseForm.reasonDesc
        });
        if (res) {
          this.opportunityStatus = "normal"; // 改变状态为正常
          this.$message.success("机会重启成功");
          this.closeRestartClosePopup();
          this.initData();
        }
      } catch (error) {
      } finally {
        this.restartCloseLoading = false;
      }
    },

    closeRestartClosePopup() {
      this.showRestartClosePopup = false;
      this.selectedRestartCloseUserId = "";
      this.selectedRestartCloseUserInfo = null; // 关闭弹窗时清空选中信息
      this.closeReasonInfo = null; // 清空关闭原因信息
      this.restartCloseBusinessType = "T0001"; // 重置为默认选择
      this.restartCloseForm.reasonDesc = "";
      this.$nextTick(() => {
        if (this.$refs.restartCloseFormRef) {
          this.$refs.restartCloseFormRef.resetFields();
        }
      });
    },

    // 关闭关闭弹窗
    closeClosePopup() {
      this.showClosePopup = false;
      this.closeForm.closeReasonType = "";
      this.closeForm.closeReason = "";
      this.$nextTick(() => {
        if (this.$refs.closeFormRef) {
          this.$refs.closeFormRef.resetFields();
        }
      });
    },



    async handleAssignSearchFun() {
      this.assignSearchForm.param.tenantId = this.$store.state.layoutStore.currentLoginUser.tenantId
      let res = await getBranchUsers(this.assignSearchForm.param);
      if (res && _.isArray(res)) {
        this.assignUserList = res;
      } else {
        this.assignUserList = [];
      }
      // 清空之前选中的用户状态
      this.selectedAssignUserId = "";
      this.selectedUserInfo = null;
    },
    async confirmAssign() {
      if (!this.selectedAssignUserId) {
        this.$message.warning("请选择指派人员");
        return;
      }

      try {
        this.assignLoading = true;
        let res = await setBranchCoordination({
          opportunityId: this.$route.query.id,
          assignee: this.selectedAssignUserId,
          assigneeRoleType: this.assignSearchForm.param.roleType,
          assigneeOrg: this.assignSearchForm.param.organCode
        });
        if (res) {
          this.closeAssignPopup();
          this.initData();
        }
      } catch (error) {
      } finally {
        this.assignLoading = false;
      }
    },
    closeAssignPopup() {
      this.showAssignPopup = false;
      this.selectedAssignUserId = "";
      this.selectedUserInfo = null; // 关闭弹窗时清空选中信息
    },

    // 任务完成确认
    async confirmTaskCompletion(taskName, node) {
      if (this.taskLoadingStatus[taskName]) {
        return;
      }
      this.taskLoadingStatus[taskName] = true;
      try {
        const currentUser = this.$store.getters["layoutStore/getCurrentLoginUser"];
        await completeTask({
          opportunityId: this.$route.query.id,
          assignee: currentUser && currentUser.userId,
          tenantId: currentUser && currentUser.tenantId,
        });
        this.initData();
      } catch (error) {
      } finally {
        this.taskLoadingStatus[taskName] = false;
      }
    },


    async confirmManager() {
      if (!this.selectedManagerUserId) {
        this.$message.warning("请选择指派人员");
        return;
      }

      try {
        this.managerLoading = true;
        let res = await setOpportunityProjecter({
          opportunityId: this.$route.query.id,
          assignee: this.selectedManagerUserId,
          assigneeRoleType: this.managerSearchForm.param.roleType,
          assigneeOrg: this.managerSearchForm.param.organCode,
          businessTenantId: this.managerBusinessType
        });
        if (res) {
          this.$message.success("指派项目经理成功");
          this.closeManagerPopup();
          this.initData();
        }
      } catch (error) {
      } finally {
        this.managerLoading = false;
      }
    },
    closeManagerPopup() {
      this.showManagerPopup = false;
      this.selectedManagerUserId = "";
      this.selectedManagerUserInfo = null; // 关闭弹窗时清空选中信息
      this.managerBusinessType = "T0001"; // 重置为默认选择
    },

    // 获取机会状态文本
    getOpportunityStatusText(status) {
      const statusMap = {
        0: '待提交',
        1: '已提交',
        2: '锁定',
        3: '暂停',
        4: '已关闭'
      };
      return statusMap[status] || '-';
    },


    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      } catch (error) {
        return dateString;
      }
    },

    // 格式化金额
    formatAmount(amount) {
      if (!amount && amount !== 0) return '-';
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    },

    // 获取关闭原因信息
    async getCloseReasonInfo() {
      try {
        const res = await getCloseReason({
          opportunityId: this.$route.query.id
        });
        if (res) {
          this.closeReasonInfo = res;
        } else {
          this.closeReasonInfo = null;
        }
      } catch (error) {
        this.closeReasonInfo = null;
      }
    },
  }
};
</script>
<style lang="less">
.base-info {
  .log-tool {
    margin-top: 20px;
  }
  .link-text{
    color: #409EFF;
    cursor: pointer;
    text-decoration: underline;
    font-size: 14px;
  } 

  .form-actions {
    margin-top: 20px;
    padding-top: 20px;
    text-align: center;
    margin-bottom: 20px;
  }

  .info-item {
    display: flex;
    flex-wrap: wrap;
    padding-left: 10px;

    .item {
      font-size: 14px;
      color: #333;
      width: 25%;
      padding: 0 10px;
      margin-bottom: 20px;
    }
  }

  .tool-wrap {
    padding: 20px 20px 0 20px;
  }

  .item-tips {
    font-size: 14px;
    color: #333;
    padding-left: 20px;
  }

  .step-wrap {
    padding: 20px;
    .info-name{
      color:v-bind('themeObj.color');
    }
    .step-list {
      margin: 40px auto 0 auto;
      border-left: 1px dashed #999;
      padding-left: 20px;
      max-width: 800px;

      .step-item {
        position: relative;
        margin-bottom: 20px;

        .radius {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          position: absolute;
          top: 2px;
          left: -28px;
        }

        .item-date {
          margin-bottom: 20px;
        }

        .step-info-wrap {
          width: 100%;
          border-radius: 10px;
          background: #f0f2f5;
          padding: 10px;

          .info-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
          }

          .info-text {
            font-size: 14px;
            line-height: 38px;

            span {
              color: #409EFF;
            }
          }

          // 任务块样式
          .task-blocks {
            margin-top: 15px;

            .task-block {
              background: #f5f7fa;
              border: 1px solid #e4e7ed;
              border-radius: 4px;
              padding: 15px;
              margin-bottom: 10px;

              .task-title {
                font-weight: bold;
                margin-bottom: 10px;
                color: #333;
              }

              .task-content {
                .execution-items {
                  margin-bottom: 8px;

                  .action-row {
                    margin-bottom: 5px;
                    display: flex;
                    align-items: center;

                    .item-label {
                      font-size: 14px;
                      color: #666;
                      margin-right: 8px;
                      flex-shrink: 0;
                    }

                    .action-item {
                      font-size: 14px;
                      color: #333;
                      margin-right: 8px;
                    }

                    .required-text {
                      font-size: 14px;
                      color: #999;
                    }
                  }
                }

                .task-completion {
                  margin-top: 10px;
                  text-align: right;
                }
              }
            }
          }
        }
      }
    }
  }

  // 弹窗通用样式
  .popup-common {
    padding: 0 20px 20px 20px;

    .description-content {
      margin-bottom: 20px;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

  // 领取机会弹窗样式
  .claim-confirm {
    @extend .popup-common;

    .confirm-content {
      margin-bottom: 20px;
      text-align: center;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  // 暂停机会弹窗样式
  .pause-form {
    @extend .popup-common;
  }

  // 启动机会弹窗样式
  .restart-form {
    @extend .popup-common;
  }

  // 关闭机会弹窗样式
  .close-form {
    @extend .popup-common;
  }

  // 通用弹窗内容样式
  .popup-content {
    padding: 0 20px 20px 20px;
    max-height: 600px;
    overflow-y: auto;

    // 在小屏幕上调整最大高度
    @media (max-height: 800px) {
      max-height: 500px;
    }

    @media (max-height: 600px) {
      max-height: 400px;
    }

    .business-type-section {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;

      .section-title {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 12px;
        color: #333;
      }

      .el-radio-group {
        .el-radio {
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .table-container {
      margin-top: 10px;
      margin-bottom: 10px;
      max-height: 300px;
      overflow-y: auto;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }

      // Firefox
      scrollbar-width: thin;
      scrollbar-color: #c1c1c1 #f1f1f1;

      // IE
      -ms-overflow-style: auto;

      // 为表格设置固定高度和滚动
      .el-table {
        .el-table__body-wrapper {
          overflow-y: auto;
        }

        // 确保表格在小屏幕上也能正常显示
        .el-table__header-wrapper {
          background-color: #f5f7fa;
        }

        .el-table__body {
          .el-table__row {
            &:hover {
              background-color: #f5f7fa;
            }
          }
        }
      }
    }

    .restart-close-tip {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;

      p {
        margin: 0;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 20px;
      text-align: center;
    }
  }

  // 指派统筹弹窗样式
  .assign-content {
    @extend .popup-content;
  }

  // 选中人员信息显示样式
  .selected-user-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f2f5;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .info-item {
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 200px;

      .label {
        font-weight: bold;
        margin-right: 8px;
        min-width: 80px;
      }

      .value {
        color: v-bind('themeObj.color');
        flex: 1;
      }
    }

    // 在小屏幕上调整布局
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 10px;

      .info-item {
        flex-direction: column;
        align-items: flex-start;
        min-width: auto;

        .label {
          margin-bottom: 4px;
          min-width: auto;
        }
      }
    }
  }

  // 重启原因输入样式
  .restart-reason-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .el-form-item {
      margin-bottom: 0;
    }

    .el-textarea {
      .el-textarea__inner {
        border: 1px solid #dcdfe6;
        border-radius: 4px;

        &:focus {
          border-color: #409eff;
        }
      }
    }
  }

  // radio按钮选中时的主题色样式
  input[type="radio"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #dcdfe6;
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;

    &:checked {
      border-color: v-bind('themeObj.color');
      background-color: v-bind('themeObj.color');

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 6px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
      }
    }

    &:hover {
      border-color: v-bind('themeObj.color');
    }
  }


}
</style>