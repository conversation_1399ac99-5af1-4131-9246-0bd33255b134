<template>
  <div class="enterprise-query-record-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="企业查询记录"
      subtitle="记录所有企业查询操作的详细信息，包括查询结果和状态"
      title-icon="el-icon-search"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="150"
      :search-label-width="'100px'"
      :show-add-button="false"
      empty-title="暂无查询记录"
      empty-description="暂无符合条件的企业查询记录"
      @search="handleSearch"
      @reset="handleReset"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
    </UniversalTable>
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable'
import { getEnterpriseCreateRecordList } from '@/api/enterprise/create/record'

export default {
  name: 'EnterpriseCreateRecord',
  components: {
    UniversalTable
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        param: {
          agentName: '',
          enterpriseName: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },

      // 搜索表单配置
      searchFormConfig: [
        {
          label: '查询人',
          name: 'agentName',
          type: 'input',
          placeholder: '请输入查询人姓名'
        },
        {
          label: '企业名称',
          name: 'enterpriseName',
          type: 'input',
          placeholder: '请输入企业名称'
        }
      ],

      // 表格列配置
      tableColumns: [
        {
          prop: 'agentName',
          label: '代理人姓名',
          width: 120,
          align: 'center'
        },
        {
          prop: 'agentCode',
          label: '代理人编码',
          width: 120,
          align: 'center'
        },
        {
          prop: 'inputEnterpriseName',
          label: '输入企业名称',
          minWidth: 180,
          showOverflowTooltip: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 160,
          align: 'center'
        },
        {
          prop: 'hasDataText',
          label: '是否有数据',
          width: 100,
          align: 'center'
        },
        {
          prop: 'isVerifyText',
          label: '是否验真',
          width: 100,
          align: 'center'
        },
        {
          prop: 'categoryName',
          label: '所属行业',
          width: 120,
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'city',
          label: '城市',
          width: 100,
          align: 'center'
        },
        {
          prop: 'isBlockedText',
          label: '是否被阻止',
          width: 100,
          align: 'center'
        }
      ],

      // 操作按钮配置
      tableActions: [
        {
          label: '查看',
          type: 'primary',
          size: 'small',
          key: 'view'
        }
      ]
    }
  },

  mounted() {
    this.loadData()
  },

  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: {
            ...this.searchForm.param
          }
        }

        const response = await getEnterpriseCreateRecordList(params)
        if (response && response.list) {
          // 处理数据，格式化所有需要显示的字段
          this.tableData = (response.list || []).map(item => ({
            ...item,
            // 处理布尔值显示
            hasDataText: item.hasData ? '是' : '否',
            isVerifyText: item.isVerify ? '是' : '否',
            isBlockedText: item.isBlocked ? '是' : '否',
            // 处理inputData中的字段
            categoryName: (item.inputData && item.inputData.categoryName) ? item.inputData.categoryName : '-',
            city: (item.inputData && item.inputData.city) ? item.inputData.city : '-'
          }))
          this.pagination.total = response.total || 0
        }
      } catch (error) {
        console.error('加载企业创建记录失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch(searchData) {
      this.searchForm.param = { ...searchData.param }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm.param = {
        agentName: '',
        enterpriseName: ''
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },

    // 操作按钮点击
    handleAction(payload) {
      const { action, row } = payload
      if (action === 'view') {
        this.handleView(row)
      }
    },

    // 查看详情
    handleView(row) {
      this.$router.push({
        name: 'EnterpriseCreateRecordDetail',
        params: { id: row.id }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.enterprise-query-record-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}
</style>
