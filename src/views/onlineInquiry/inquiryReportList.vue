<template>
  <div class="report-list">

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
                @normalResetQuery="normalResetQuery"></SearchForm>

    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover stripe>
      <el-table-column align="center" prop="companyName" label="保险公司"></el-table-column>
      <el-table-column align="center" prop="productCategory" label="产品大类"></el-table-column>
      <el-table-column align="center" prop="productName" label="产品名称"></el-table-column>
      <el-table-column align="center" label="手续费/经纪费">
        <template slot-scope="scope">
          {{ scope.row.handlingFee }}%
        </template>
      </el-table-column>
      <el-table-column align="center" prop="productSolution" label="产品方案/形态"></el-table-column>
      <el-table-column align="center" prop="reportingAgency" label="上报机构"></el-table-column>
      <el-table-column align="center" prop="businessName" label="业务主体"></el-table-column>
      <el-table-column align="center" prop="reporter" label="上报人"></el-table-column>
      <el-table-column align="center" label="上报状态">
        <template slot-scope="scope">
          {{ scope.row.approvalStatus | getDicItemName("elms.approval.status") }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="view(scope.row)">查看</el-button>
          <el-button class="btn-center" type="text" v-if="scope.row.approvalStatus == '0'" @click="approval(scope.row)">重新提交</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
                :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>


    <!-- 新增上报 -->
    <DtPopup :isShow.sync="showPopup" @close="closePopup" width="70%" title="新增上报" :footer="false">
      <el-form ref="addForm" :model="addData" label-width="120px" :rules="addRules" label-position="left">
        <div style="max-height:70vh;overflow:auto">
          <el-form-item>
            <template slot="label">
              <span style="color: red;padding-right:3px;">*</span>保险公司
            </template>
            <el-select v-model="addData.inquiryCompany" placeholder="请选择保险公司" class="full-width">
              <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode" :label="template.dicItemName" :value="template.dicItemCode"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <template slot="label">
              <span style="color: red;padding-right:3px;">*</span>上报产品
            </template>
            <el-table :data="productData" class="dt-table" style="width: 90%" v-hover>
              <el-table-column align="center" label="产品大类">
                <template slot-scope="scope">
                <el-select v-model="scope.row.productCategoryCode" placeholder="请选择产品大类" @change="changeCategory(scope.row)">
                  <el-option v-for="item in productCategoryList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" label="产品名称">
                <template slot-scope="scope">
                <el-select v-model="scope.row.productCode" placeholder="请选择产品名称">
                  <el-option v-for="item in productNameList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="handlingFee" label="手续费/经纪费">
                <template slot-scope="scope">
                <el-input v-model="scope.row.handlingFee" type="number" placeholder="请输入手续费比例" style="width: 165px"></el-input> %
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="上报机构">
            <template>
              {{ reportCompany }}
            </template>
          </el-form-item>
          <el-form-item label="上报人" prop="roleType">
            <template>
              {{ reportor }}
            </template>
          </el-form-item>
          <el-form-item label="产品方案/形态">
            <div class="product-plan-config">
              <div class="head-item" v-if="productPlanFileList.length < 10">
                <div class="detail-upload-btn">
                  <el-upload
                    icon="el-icon-upload2"
                    class="upload-demo downd_excedl"
                    :multiple="false"
                    style="display: inline-block; margin: 0 10px"
                    :auto-upload="true"
                    list-type="text"
                    :data="detailFileParam"
                    :show-file-list="false"
                    :action="fileUploadUrl"
                    :limit="10"
                    :on-exceed="handleExceed"
                    :before-upload="inquiryBeforeUpload"
                    :on-success="uploadSuccess"
                    :headers="{
                    access_token: currentLoginUser.access_token,
                    tenantId: currentLoginUser.tenantId,
                    funcId: currentLoginUser.funcId,}">
                    <el-button type="primary">上传文件</el-button>
                  </el-upload>
                  <span style="font-size: 12px;color: #C7C7C7;">支持PDF/WORD/PPT/EXCEL/PNG/JPG/ZIP/RAR/7Z格式文件，最多10个，单个文件不大于20M</span>
                </div>

              </div>
              <div class="product-item" v-for="(item, index) in productPlanFileList" :key="index">
                <div style="width: 60%; padding-left:10px;">
                  <span>{{item.fileName}}</span>
                </div>
                <div style="width: 35%;text-align: right">
                  <i class="el-icon-delete"></i>
                  <el-button type="text" @click="deleteProductPlanFile(item, index)" style="padding-left: 5px;">删除</el-button>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="备注" prop="notes">
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8}" placeholder="请输入上报备注" v-model="addData.notes" maxlength="150字"
                      show-word-limit style="width: 90%"></el-input>
          </el-form-item>
        </div>
        <div style="padding:20px 0;text-align: center">
          <el-button type="primary" class="btn-width"
                     :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px',width:'120px' }"
                     @click="closePopup">取消</el-button>
          <el-button type="primary" class="btn-width"
                     :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color,width:'120px' }"
                     @click="confirm">确认</el-button>
        </div>
      </el-form>
    </DtPopup>

    <!-- 查看上报 -->
    <DtPopup :isShow.sync="showViewPopup" @close="closeViewPopup" width="70%" title="查看上报" :footer="false">
      <el-form ref="addForm" :model="addData" label-width="120px" label-position="left">
        <div style="max-height:70vh;overflow:auto">
          <el-form-item>
            <template slot="label">
              <span style="color: red;padding-right:3px;">*</span>保险公司
            </template>
            <el-select v-model="addData.inquiryCompany" placeholder="请选择保险公司" class="full-width" disabled>
              <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode" :label="template.dicItemName" :value="template.dicItemCode"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <template slot="label">
              <span style="color: red;padding-right:3px;">*</span>上报产品
            </template>
            <el-table :data="productData" class="dt-table" style="width: 90%" v-hover>
              <el-table-column align="center" label="产品大类">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.productCategoryCode" placeholder="请选择产品大类" disabled>
                    <el-option v-for="item in productCategoryList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" label="产品名称">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.productCode" placeholder="请选择产品名称" disabled>
                    <el-option v-for="item in productNameList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="handlingFee" label="手续费/经纪费">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.handlingFee" type="number" placeholder="请输入手续费比例" style="width: 165px" disabled></el-input> %
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="上报机构">
            <template>
              {{ reportCompany }}
            </template>
          </el-form-item>
          <el-form-item label="上报人" prop="roleType">
            <template>
              {{ reportor }}
            </template>
          </el-form-item>
          <el-form-item label="产品方案/形态">
            <div class="product-plan-config">
              <div class="product-item" v-for="(item, index) in productPlanFileList" :key="index">
                <div style="width: 60%; padding-left:10px;">
                  <span>{{item.fileName}}</span>
                </div>
                <div style="width: 35%;text-align: right">
                  <i class="el-icon-delete"></i>
                  <el-button type="text" @click="download(item, index)" style="padding-left: 5px;">下载</el-button>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="备注" prop="notes">
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8}" placeholder="请输入上报备注" v-model="addData.notes" maxlength="150字"
                      show-word-limit style="width: 90%" disabled></el-input>
          </el-form-item>
        </div>
        <div style="padding:20px 0;text-align: center">
          <el-button type="primary" class="btn-width"
                     :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px',width:'120px' }"
                     @click="closeViewPopup">关 闭</el-button>
        </div>
      </el-form>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/roleManagement/index.js";
import {validate, validateAlls} from "@/config/validation";
import {getDicItemList} from "@/config/tool.js";
import {getCurrentUserTenantInfo} from "@/api/userManagement";
import {rootPath} from "@/utils/globalParam";
import _ from "lodash";

export default {
  name: "inquiryReportList",
  data() {
    return {
      toolListProps: {
        toolTitle: "询价产品上报",
        toolList: [
          {
            name: "新增上报",
            icon: "iconfont icondt8",
            // btnCode: "elms:role:add"
          }
        ]
      },
      tableData: [],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleName: ""
        }
      },
      searchFormTemp: [
        {
          label: "保险公司",
          name: "roleName",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "上报状态",
          name: "roleType",
          type: "select",
          width: "200px",
          list:[]
        }
      ],
      total: 0,
      legalList:[],
      showPopup:false,// 新增修改弹窗
      showViewPopup:false,// 查看弹窗
      addData: {
        inquiryCompany: "",
        productCategoryCode: "",
        productCategoryName: "",
        productCode: "",
        productName: "",
        handlingFee: "",
        notes:"",
        productFiles:[]
      },
      addRules: {
        inquiryCompany: [{ required: true, validator: validate, trigger: "blur"}]
      },
      inquiryCompanyList:[],
      reportCompany:"",
      reportor:"",
      productPlanList:[],
      productPlanUrlList:[],
      productPlanFileList:[],
      productCategoryList:[],
      productNameList:[],
      productData:[],
      fileUploadUrl:`${rootPath}/api/upload/file`,
      detailFileParam:{
        fileType:"inquiryProduct",
      },
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
  },
  filters: {
    getRoleType(list, roleType) {
      let index = list.findIndex((v) => v.dicItemCode == roleType);
      return index > -1 ? list[index].dicItemName : "-";
    }
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    async getDicFun() {
      // 询价公司
      this.searchFormTemp[0].list = this.inquiryCompanyList = await getDicItemList("elms.inquiry.company");
      console.log(this.inquiryCompanyList,'-----------')

      this.searchFormTemp[1].list = await getDicItemList("elms.approval.status");

      this.productData=[];
      this.productData.push({index:1,productCategory:"",productCode:"",handlingFee:""});

      this.productCategoryList = [
        {"value":"产品大类1-value","label":"产品大类1"},
        {"value":"产品大类2-value","label":"产品大类2"},
        {"value":"产品大类3-value","label":"产品大类3"},
        {"value":"产品大类4-value","label":"产品大类4"},
        {"value":"产品大类5-value","label":"产品大类5"},
      ];

    },
    async initData() {

      this.tableData = [
        {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"1"},
        {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"2"},
        {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"0"},
        {"companyName":"平安财险","productCategory":"企财险","productName":"财产一切险","handlingFee":"15","productSolution":"平安财产一切险产品详情.pdf","reportingAgency":"上海分公司","businessName":"大童经纪","reporter":"上报人","approvalStatus":"2"}
      ];
      this.total = this.tableData.length;

      // this.initParam.param.tenantId = this.$store.state.layoutStore.currentLoginUser.tenantId;
      // let res = await api.getRolePage(this.initParam);
      // if (res) {
      //   this.total = res.total;
      //   this.tableData = [];
      //   if (res.list) {
      //     this.tableData = res.list ? res.list : [{}];
      //   }
      // }
    },
    async handleTool(item) {
      if (item.name == "新增上报") {
        let res = await getCurrentUserTenantInfo();
        if (res) {
          this.reportCompany = res.orgName;
          this.reportor = res.userName;
        }
        this.showPopup = true;
      }
    },
    addProductPlanFile(){},
    deleteProductPlanFile(item, index){
      this.productPlanFileList.splice(index, 1);
    },
    handleExceed() {
      this.$message.warning(`最多上传 10 个文件`);
    },
    inquiryBeforeUpload(file) {
      if (!(
        this._.endsWith(file.name, "pdf") ||
        this._.endsWith(file.name, "ppt") ||
        this._.endsWith(file.name, "pptx") ||
        this._.endsWith(file.name, "xls") ||
        this._.endsWith(file.name, "xlsx") ||
        this._.endsWith(file.name, "doc") ||
        this._.endsWith(file.name, "docx") ||
        this._.endsWith(file.name, "png") ||
        this._.endsWith(file.name, "jpg") ||
        this._.endsWith(file.name, "zip") ||
        this._.endsWith(file.name, "rar") ||
        this._.endsWith(file.name, "7z"))
      ) {
        this.$message.error("文件格式不正确");
        return false;
      }

      let size = file.size / 1024 / 1024;
      if (size > 20) {
        this.$message.error("单个文件不能超过20M");
        return;
      }
      this.detailFileParam.fileType="inquiry";
      return true;
    },

    // 上传成功
    uploadSuccess(response, file) {
      console.log(response,'---------response------------');
      console.log(file,'-----------file-----------');
      this.productPlanFileList.push({
        fileName: file.name,
        fileUrl: response.datas
      });
    },



    closePopup() {
      this.addData = _.cloneDeep(this.$options.data().addData);
      this.showPopup = false;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      });
    },

    closeViewPopup(){
      this.showViewPopup = false;
    },

    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },

    changeCategory(row){
      console.log(row.productCategoryCode,'-------val------')
      this.addData.productCode = '';
      this.addData.productName = '';
      this.productData[row.index-1].productCode = '';
      let val = row.productCategoryCode;
      this.productNameList = [
        {"value":"产品1-"+val,"label":"产品1"+val},
        {"value":"产品2-"+val,"label":"产品2"+val},
        {"value":"产品3-"+val,"label":"产品3"+val},
        {"value":"产品4-"+val,"label":"产品4"+val},
        {"value":"产品5-"+val,"label":"产品5"+val}
      ];

    },

    async confirm() {
      // if (!validateAlls(this.$refs.addForm)){return;}

      if(this.addData.inquiryCompany == '') {
        this.$message.error("保险公司不能为空");
        return;
      }

      if (!this.productData[0].productCategoryCode || this.productData[0].productCategoryCode == '') {
        this.$message.error("产品大类不能为空");
        return;
      }

      if (!this.productData[0].productCode || this.productData[0].productCode == '') {
        this.$message.error("产品名称不能为空");
        return;
      }

      if (!this.productData[0].handlingFee || this.productData[0].handlingFee == '') {
        this.$message.error("手续费/经纪费不能为空");
        return;
      }else {
        if(!/^\d+$/.test(this.productData[0].handlingFee)) {
          this.$message.error("手续费/经纪费只能输入数字");
          return;
        }
      }

      if(this.productPlanFileList.length == 0) {
        this.$message.error("产品方案/形态不能为空");
        return;
      }

      let categoryObj = _.find(this.productCategoryList, el => {
        return el.value == this.productData[0].productCategoryCode;
      });

      if(categoryObj) {
        this.addData.productCategoryName = categoryObj.label;
      }

      let productObj = _.find(this.productNameList, el => {
        return el.value == this.productData[0].productCode;
      });
      if(productObj) {
        this.addData.productName = productObj.label;
      }
      this.addData.productCategoryCode = this.productData[0].productCategoryCode;
      this.addData.productCode = this.productData[0].productCode;
      this.addData.handlingFee = this.productData[0].handlingFee;
      this.addData.productFiles = this.productPlanFileList;
      console.log(this.addData,'-----addData-----')
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    },
    view(row) {
      this.initData();
      this.showViewPopup = true;
    },
  }
};
</script>
<style lang="less">
.approval-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }

}

.product-plan-config {
  //width: 80%;
}

.product-item {
  display: flex;
  background-color: rgba(242, 242, 242, 1);
  margin-top: 20px;
  border-radius: 5px;
  width: 90%;
}

</style>
