<template>
  <div class="quill-editor-wrapper">
    <!-- 编辑器容器 -->
    <div
      v-if="!readonly"
      :class="[
        'quill-editor-container',
        { 'is-disabled': disabled },
        { 'is-focus': isFocused },
        { 'is-error': hasError }
      ]"
    >
      <!-- 工具栏 -->
      <div ref="toolbar" class="quill-toolbar">
        <!-- 字体格式 -->
        <span class="ql-formats">
          <select class="ql-header" v-if="options.header">
            <option selected></option>
            <option value="1"></option>
            <option value="2"></option>
            <option value="3"></option>
          </select>
          <select class="ql-size" v-if="options.size">
            <option value="small"></option>
            <option selected></option>
            <option value="large"></option>
            <option value="huge"></option>
          </select>
        </span>

        <!-- 文本样式 -->
        <span class="ql-formats">
          <button class="ql-bold" v-if="options.bold"></button>
          <button class="ql-italic" v-if="options.italic"></button>
          <button class="ql-underline" v-if="options.underline"></button>
          <button class="ql-strike" v-if="options.strike"></button>
        </span>

        <!-- 颜色 -->
        <span class="ql-formats" v-if="options.color">
          <select class="ql-color"></select>
          <select class="ql-background"></select>
        </span>

        <!-- 列表和缩进 -->
        <span class="ql-formats">
          <button class="ql-list" value="ordered" v-if="options.list"></button>
          <button class="ql-list" value="bullet" v-if="options.list"></button>
          <button class="ql-indent" value="-1" v-if="options.indent"></button>
          <button class="ql-indent" value="+1" v-if="options.indent"></button>
        </span>

        <!-- 对齐 -->
        <span class="ql-formats" v-if="options.align">
          <select class="ql-align"></select>
        </span>

        <!-- 链接和图片 -->
        <span class="ql-formats">
          <button class="ql-link" v-if="options.link"></button>
          <button class="ql-image" v-if="options.image"></button>
        </span>

        <!-- 清除格式 -->
        <span class="ql-formats" v-if="options.clean">
          <button class="ql-clean"></button>
        </span>
      </div>

      <!-- 编辑器内容区域 -->
      <div
        ref="editor"
        class="quill-editor-content"
        :style="{ minHeight: editorHeight }"
      ></div>

      <!-- 字数统计 -->
      <div v-if="showWordCount" class="quill-word-count">
        <span class="word-count-text">
          {{ currentLength }}{{ maxLength ? `/${maxLength}` : '' }} 字符
        </span>
      </div>
    </div>

    <!-- 只读模式 -->
    <div
      v-else
      class="quill-editor-readonly"
      :style="{ minHeight: editorHeight }"
    >
      <div ref="readonlyContent" class="ql-editor"></div>
    </div>

    <!-- 错误提示 -->
    <div v-if="hasError && errorMessage" class="quill-error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script>
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

export default {
  name: 'QuillEditor',
  props: {
    // 编辑器内容
    value: {
      type: String,
      default: ''
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    // 编辑器高度
    height: {
      type: [String, Number],
      default: 200
    },
    // 工具栏配置
    toolbar: {
      type: [Array, Object, String],
      default: () => 'full'
    },
    // 编辑器选项
    options: {
      type: Object,
      default: () => ({
        header: true,
        size: false,
        bold: true,
        italic: true,
        underline: true,
        strike: true,
        color: true,
        list: true,
        indent: true,
        align: true,
        link: true,
        image: true,
        clean: true
      })
    },
    // 最大长度限制
    maxLength: {
      type: Number,
      default: null
    },
    // 是否显示字数统计
    showWordCount: {
      type: Boolean,
      default: false
    },
    // 图片上传处理函数
    imageHandler: {
      type: Function,
      default: null
    },
    // 内容格式 'html' | 'delta' | 'text'
    contentType: {
      type: String,
      default: 'html',
      validator: value => ['html', 'delta', 'text'].includes(value)
    }
  },
  data() {
    return {
      quill: null,
      isFocused: false,
      hasError: false,
      errorMessage: '',
      currentLength: 0,
      internalValue: ''
    }
  },
  computed: {
    // 编辑器高度
    editorHeight() {
      if (typeof this.height === 'number') {
        return `${this.height}px`
      }
      return this.height || '200px'
    },
    // 工具栏配置
    toolbarOptions() {
      if (this.toolbar === 'full') {
        return [
          [{ 'header': [1, 2, 3, false] }],
          ['bold', 'italic', 'underline', 'strike'],
          [{ 'color': [] }, { 'background': [] }],
          [{ 'list': 'ordered' }, { 'list': 'bullet' }],
          [{ 'indent': '-1' }, { 'indent': '+1' }],
          [{ 'align': [] }],
          ['link', 'image'],
          ['clean']
        ]
      } else if (this.toolbar === 'minimal') {
        return [
          ['bold', 'italic', 'underline'],
          [{ 'list': 'ordered' }, { 'list': 'bullet' }],
          ['link'],
          ['clean']
        ]
      }
      return this.toolbar
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.readonly) {
          // 只读模式下直接更新内容
          this.updateReadonlyContent(newVal)
        } else if (this.quill && newVal !== this.internalValue) {
          this.setContent(newVal)
        }
      },
      immediate: true
    },
    disabled(newVal) {
      if (this.quill) {
        this.quill.enable(!newVal)
      }
    }
  },
  mounted() {
    this.initQuill()
  },
  beforeDestroy() {
    if (this.quill) {
      this.quill = null
    }
  },
  methods: {
    // 初始化 Quill 编辑器
    initQuill() {
      if (this.readonly) {
        this.initReadonlyMode()
        return
      }

      const options = {
        theme: 'snow',
        placeholder: this.placeholder,
        readOnly: this.disabled,
        modules: {
          toolbar: {
            container: this.$refs.toolbar,
            handlers: {
              image: this.imageHandler || this.defaultImageHandler
            }
          }
        }
      }

      this.quill = new Quill(this.$refs.editor, options)

      // 设置初始内容
      if (this.value) {
        this.setContent(this.value)
      }

      // 绑定事件
      this.bindEvents()
    },

    // 初始化只读模式
    initReadonlyMode() {
      this.updateReadonlyContent(this.value)
    },

    // 更新只读模式内容
    updateReadonlyContent(content) {
      this.$nextTick(() => {
        if (this.$refs.readonlyContent) {
          this.$refs.readonlyContent.innerHTML = content || ''
        }
      })
    },

    // 绑定编辑器事件
    bindEvents() {
      if (!this.quill) return

      // 内容变化事件
      this.quill.on('text-change', (delta, oldDelta, source) => {
        const content = this.getContent()
        this.internalValue = content
        this.updateLength()
        this.validateContent()
        this.$emit('input', content)
        this.$emit('change', { content, delta, source })
      })

      // 选择变化事件
      this.quill.on('selection-change', (range, oldRange, source) => {
        if (range) {
          this.isFocused = true
          this.$emit('focus', { range, source })
        } else {
          this.isFocused = false
          this.$emit('blur', { range: oldRange, source })
        }
        this.$emit('selection-change', { range, oldRange, source })
      })
    },

    // 默认图片处理器
    defaultImageHandler() {
      const input = document.createElement('input')
      input.setAttribute('type', 'file')
      input.setAttribute('accept', 'image/*')
      input.click()

      input.onchange = () => {
        const file = input.files[0]
        if (file) {
          this.handleImageUpload(file)
        }
      }
    },

    // 处理图片上传
    handleImageUpload(file) {
      if (this.imageHandler) {
        this.imageHandler(file, (url) => {
          this.insertImage(url)
        })
      } else {
        // 默认转换为 base64
        const reader = new FileReader()
        reader.onload = (e) => {
          this.insertImage(e.target.result)
        }
        reader.readAsDataURL(file)
      }
    },

    // 插入图片
    insertImage(url) {
      if (this.quill) {
        const range = this.quill.getSelection()
        this.quill.insertEmbed(range ? range.index : 0, 'image', url)
      }
    },

    // 获取内容
    getContent() {
      if (!this.quill) return ''

      switch (this.contentType) {
        case 'html':
          return this.quill.root.innerHTML
        case 'delta':
          return JSON.stringify(this.quill.getContents())
        case 'text':
          return this.quill.getText()
        default:
          return this.quill.root.innerHTML
      }
    },

    // 设置内容
    setContent(content) {
      if (!this.quill || content === null || content === undefined) return

      try {
        switch (this.contentType) {
          case 'html':
            if (content !== this.quill.root.innerHTML) {
              this.quill.clipboard.dangerouslyPasteHTML(content)
            }
            break
          case 'delta':
            this.quill.setContents(JSON.parse(content))
            break
          case 'text':
            this.quill.setText(content)
            break
          default:
            if (content !== this.quill.root.innerHTML) {
              this.quill.clipboard.dangerouslyPasteHTML(content)
            }
        }
        this.updateLength()
      } catch (error) {
        console.error('设置编辑器内容失败:', error)
      }
    },

    // 更新字符长度
    updateLength() {
      if (this.quill) {
        this.currentLength = this.quill.getText().length - 1 // 减去末尾的换行符
      }
    },

    // 验证内容
    validateContent() {
      this.hasError = false
      this.errorMessage = ''

      if (this.maxLength && this.currentLength > this.maxLength) {
        this.hasError = true
        this.errorMessage = `内容长度不能超过 ${this.maxLength} 个字符`
      }
    },

    // 获取纯文本
    getText() {
      return this.quill ? this.quill.getText() : ''
    },

    // 获取 HTML
    getHTML() {
      return this.quill ? this.quill.root.innerHTML : ''
    },

    // 获取 Delta
    getDelta() {
      return this.quill ? this.quill.getContents() : null
    },

    // 清空内容
    clear() {
      if (this.quill) {
        this.quill.setText('')
      }
    },

    // 聚焦编辑器
    focus() {
      if (this.quill) {
        this.quill.focus()
      }
    },

    // 失焦编辑器
    blur() {
      if (this.quill) {
        this.quill.blur()
      }
    },

    // 插入文本
    insertText(text, index) {
      if (this.quill) {
        const insertIndex = index !== undefined ? index : this.quill.getLength()
        this.quill.insertText(insertIndex, text)
      }
    },

    // 格式化文本
    format(name, value) {
      if (this.quill) {
        this.quill.format(name, value)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.quill-editor-wrapper {
  .quill-editor-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    transition: border-color 0.2s;

    &.is-focus {
      border-color: #409eff;
    }

    &.is-error {
      border-color: #f56c6c;
    }

    &.is-disabled {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      cursor: not-allowed;
    }
  }

  .quill-toolbar {
    border-bottom: 1px solid #dcdfe6;
    background-color: #fafafa;
  }

  .quill-editor-content {
    background-color: #fff;
  }

  .quill-word-count {
    padding: 8px 12px;
    border-top: 1px solid #dcdfe6;
    background-color: #fafafa;
    text-align: right;

    .word-count-text {
      font-size: 12px;
      color: #909399;
    }
  }

  .quill-editor-readonly {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f5f7fa;
    padding: 12px;
  }

  .quill-error-message {
    margin-top: 4px;
    font-size: 12px;
    color: #f56c6c;
  }
}

// Quill 样式覆盖
::v-deep .ql-toolbar {
  border: none;
  padding: 8px 12px;

  .ql-formats {
    margin-right: 15px;
  }
}

::v-deep .ql-container {
  border: none;
  font-size: 14px;
}

::v-deep .ql-editor {
  padding: 12px;
  line-height: 1.6;
  color: #606266;
  min-height: inherit;

  &.ql-blank::before {
    color: #c0c4cc;
    font-style: normal;
  }

  p {
    margin-bottom: 8px;
  }

  ul, ol {
    margin-bottom: 8px;
  }

  blockquote {
    border-left: 4px solid #dcdfe6;
    padding-left: 16px;
    margin: 8px 0;
    color: #909399;
  }

  pre {
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 12px;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  a {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
