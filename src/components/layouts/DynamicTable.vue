<template>
  <div class="dynamic-table-container">
    <!-- 表格标题和操作按钮 -->
    <div class="table-header">
      <div class="header-left">
        <div class="title-text">
          <i v-if="icon" :class="icon"></i>{{ title }}
        </div>
      </div>
      <div class="header-right" v-if="!isView">
        <!-- 列管理按钮 -->
        <el-button
          size="mini"
          @click="showColumnManager = true"
          class="action-btn column-btn"
          title="管理列"
        >
          <i class="el-icon-s-grid"></i>
          管理列
        </el-button>
        <!-- 添加行按钮 -->
        <el-button
          size="mini"
          @click="addRow"
          class="action-btn add-btn"
          title="添加行"
        >
          <i class="el-icon-plus"></i>
          添加行
        </el-button>
      </div>
    </div>

    <!-- 动态表格 -->
    <el-table
      :data="tableData"
      stripe
      class="modern-table"
      :class="{ 'view-mode': isView }"
      :empty-text="emptyText || '暂无数据'"
    >
      <!-- 动态列 -->
      <el-table-column
        v-for="column in columns"
        :key="column.key"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth || 120"
        :align="column.align || 'center'"
      >
        <template slot-scope="scope">
          <template v-if="!isView">
            <!-- 输入框 -->
            <el-input
              v-if="column.type === 'input' || !column.type"
              v-model="scope.row[column.key]"
              :placeholder="column.placeholder || `请输入${column.label}`"
              size="mini"
            />
            <!-- 下拉选择 -->
            <el-select
              v-else-if="column.type === 'select'"
              v-model="scope.row[column.key]"
              :placeholder="column.placeholder || `请选择${column.label}`"
              size="mini"
              style="width: 100%;"
            >
              <el-option
                v-for="option in column.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <!-- 数字输入 -->
            <el-input-number
              v-else-if="column.type === 'number'"
              v-model="scope.row[column.key]"
              :placeholder="column.placeholder || `请输入${column.label}`"
              size="mini"
              style="width: 100%;"
            />
            <!-- 文本域 -->
            <el-input
              v-else-if="column.type === 'textarea'"
              v-model="scope.row[column.key]"
              type="textarea"
              :rows="2"
              :placeholder="column.placeholder || `请输入${column.label}`"
              size="mini"
            />
          </template>
          <!-- 查看模式 -->
          <template v-else>
            <div class="view-value">{{ formatCellValue(scope.row[column.key], column) }}</div>
          </template>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column
        v-if="!isView"
        label="操作"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-delete"
            size="mini"
            @click="deleteRow(scope.$index)"
            style="color: #f56c6c;"
            title="删除行"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 列管理对话框 -->
    <el-dialog
      title="管理列"
      :visible.sync="showColumnManager"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="column-manager">
        <div class="manager-header">
          <span>列配置</span>
          <el-button
            size="mini"
            type="primary"
            @click="addColumn"
            icon="el-icon-plus"
          >
            添加列
          </el-button>
        </div>

        <el-table :data="editableColumns" class="column-config-table">
          <el-table-column label="列标识" width="120">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.key"
                size="mini"
                placeholder="列标识"
                :disabled="scope.row.isDefault"
              />
            </template>
          </el-table-column>

          <el-table-column label="列名称" width="120">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.label"
                size="mini"
                placeholder="列名称"
              />
            </template>
          </el-table-column>

          <el-table-column label="类型" width="100">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.type"
                size="mini"
                style="width: 100%;"
              >
                <el-option label="文本" value="input" />
                <el-option label="选择" value="select" />
                <el-option label="数字" value="number" />
                <el-option label="文本域" value="textarea" />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="宽度" width="80">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.width"
                size="mini"
                :min="80"
                :max="300"
                controls-position="right"
              />
            </template>
          </el-table-column>

          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button
                v-if="!scope.row.isDefault"
                type="text"
                icon="el-icon-delete"
                size="mini"
                @click="deleteColumn(scope.$index)"
                style="color: #f56c6c;"
              >
                删除
              </el-button>
              <span v-else style="color: #909399; font-size: 12px;">默认</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelColumnEdit">取消</el-button>
        <el-button type="primary" @click="saveColumnConfig">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DynamicConfigTable',
  props: {
    title: {
      type: String,
      default: '动态表格'
    },
    icon: {
      type: String,
      default: ''
    },
    value: {
      type: Object,
      default: () => ({
        columnDefs: [],
        rowData: []
      })
    },
    defaultColumns: {
      type: Array,
      default: () => []
    },
    isView: {
      type: Boolean,
      default: false
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    }
  },
  data() {
    return {
      showColumnManager: false,
      editableColumns: []
    }
  },
  computed: {
    columns() {
      return this.value.columnDefs || this.defaultColumns
    },
    tableData: {
      get() {
        return this.value.rowData || []
      },
      set(val) {
        this.$emit('input', {
          ...this.value,
          rowData: val
        })
      }
    }
  },
  watch: {
    value: {
      handler() {
        this.initEditableColumns()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 初始化可编辑列配置
    initEditableColumns() {
      this.editableColumns = (this.columns || []).map(col => ({
        ...col,
        isDefault: this.defaultColumns.some(defaultCol => defaultCol.key === col.key)
      }))
    },

    // 添加行
    addRow() {
      const newRow = {}
      this.columns.forEach(col => {
        newRow[col.key] = ''
      })

      const newData = [...this.tableData, newRow]
      this.tableData = newData
    },

    // 删除行
    deleteRow(index) {
      this.$confirm('确定要删除这一行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const newData = [...this.tableData]
        newData.splice(index, 1)
        this.tableData = newData
        this.$message.success('删除成功')
      }).catch(() => {})
    },

    // 添加列
    addColumn() {
      this.editableColumns.push({
        key: `custom_${Date.now()}`,
        label: '新列',
        type: 'input',
        width: 120,
        isDefault: false
      })
    },

    // 删除列
    deleteColumn(index) {
      this.editableColumns.splice(index, 1)
    },

    // 取消列编辑
    cancelColumnEdit() {
      this.showColumnManager = false
      this.initEditableColumns()
    },

    // 保存列配置
    saveColumnConfig() {
      // 验证列配置
      const hasEmptyKey = this.editableColumns.some(col => !col.key || !col.label)
      if (hasEmptyKey) {
        this.$message.error('请填写完整的列标识和列名称')
        return
      }

      // 检查重复的key
      const keys = this.editableColumns.map(col => col.key)
      const uniqueKeys = [...new Set(keys)]
      if (keys.length !== uniqueKeys.length) {
        this.$message.error('列标识不能重复')
        return
      }

      // 更新列配置
      const newColumnDefs = this.editableColumns.map(col => ({
        key: col.key,
        label: col.label,
        type: col.type || 'input',
        width: col.width || 120
      }))

      // 更新行数据，确保新列有对应的字段
      const newRowData = this.tableData.map(row => {
        const newRow = { ...row }
        newColumnDefs.forEach(col => {
          if (!(col.key in newRow)) {
            newRow[col.key] = ''
          }
        })
        return newRow
      })

      this.$emit('input', {
        columnDefs: newColumnDefs,
        rowData: newRowData
      })

      this.showColumnManager = false
      this.$message.success('列配置保存成功')
    },

    // 格式化单元格值
    formatCellValue(value, column) {
      if (column.options && value) {
        const option = column.options.find(opt => opt.value === value)
        return option ? option.label : value
      }
      return value || '-'
    }
  }
}
</script>

<style lang="less" scoped>
.dynamic-table-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .header-left {
      .title-text {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        color: #303133;

        i {
          margin-right: 8px;
          color: #FF8030;
          font-size: 18px;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 8px;

      .action-btn {
        border-radius: 4px;
        font-size: 12px;
        padding: 6px 12px;

        &.column-btn {
          background: #FF8030;
          border-color: #FF8030;
          color: white;

          &:hover {
            background: #e6722a;
            border-color: #e6722a;
          }
        }

        &.add-btn {
          background: #67c23a;
          border-color: #67c23a;
          color: white;

          &:hover {
            background: #5daf34;
            border-color: #5daf34;
          }
        }
      }
    }
  }

  .modern-table {
    border: 1px solid #ebeef5;
    border-radius: 6px;
    overflow: hidden;

    /deep/ .el-table__header {
      background: #fafafa;

      th {
        background: #fafafa !important;
        color: #606266;
        font-weight: 600;
        border-bottom: 1px solid #ebeef5;
      }
    }

    /deep/ .el-table__body {
      tr {
        &:hover {
          background: #f5f7fa;
        }

        td {
          border-bottom: 1px solid #f0f0f0;

          .el-input,
          .el-select,
          .el-input-number {
            .el-input__inner {
              border: 1px solid #dcdfe6;
              border-radius: 4px;

              &:focus {
                border-color: #FF8030;
              }
            }
          }
        }
      }
    }

    &.view-mode {
      /deep/ .el-table__body {
        tr td {
          .view-value {
            padding: 8px 0;
            color: #303133;
            font-weight: 500;
          }
        }
      }
    }
  }

  .column-manager {
    .manager-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #ebeef5;

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .column-config-table {
      /deep/ .el-table__header {
        background: #f8f9fa;

        th {
          background: #f8f9fa !important;
          color: #606266;
          font-weight: 600;
        }
      }

      /deep/ .el-input,
      /deep/ .el-select,
      /deep/ .el-input-number {
        .el-input__inner {
          border: 1px solid #dcdfe6;
          border-radius: 4px;

          &:focus {
            border-color: #FF8030;
          }
        }
      }
    }
  }
}

/deep/ .el-dialog {
  .el-dialog__header {
    background: #f8f9fa;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;

    .el-dialog__title {
      color: #303133;
      font-weight: 600;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 12px 20px;
    border-top: 1px solid #ebeef5;
    background: #f8f9fa;
  }
}
</style>
