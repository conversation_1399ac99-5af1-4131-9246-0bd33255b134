<template>
  <!-- 面包屑导航 - 按钮风格 -->
  <div class="breadcrumb-section" v-if="showBreadcrumb && breadcrumbItems.length > 0">
    <div class="breadcrumb-buttons">
      <template v-for="(item, index) in breadcrumbItems">
        <!-- 可点击的面包屑按钮 -->
        <el-button
          v-if="item.to"
          :key="`btn-${index}`"
          @click="handleBreadcrumbClick(item)"
          class="breadcrumb-btn clickable-btn"
          size="mini"
        >
          <i :class="item.icon" v-if="item.icon"></i>
          {{ item.text }}
        </el-button>

        <!-- 当前页面按钮（不可点击） -->
        <el-button
          v-else
          :key="`current-${index}`"
          class="breadcrumb-btn current-btn"
          size="mini"
          disabled
        >
          <i :class="item.icon" v-if="item.icon"></i>
          {{ item.text }}
        </el-button>

        <!-- 分隔符箭头 -->
        <i
          v-if="index < breadcrumbItems.length - 1"
          :key="`separator-${index}`"
          class="el-icon-arrow-right breadcrumb-separator"
        ></i>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: "BreadcrumbNavigation",
  props: {
    // 是否显示面包屑
    showBreadcrumb: {
      type: Boolean,
      default: true
    },
    // 面包屑数据
    breadcrumbItems: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 处理面包屑点击
    handleBreadcrumbClick(item) {
      this.$emit('breadcrumb-click', item);

      if (item.to) {
        // 检查是否要跳转到当前页面，避免重复导航错误
        const targetRoute = typeof item.to === 'string' ? { name: item.to } : item.to;
        const currentRoute = this.$route;

        // 简单比较路由名称，避免重复跳转
        if (typeof item.to === 'string' && currentRoute.name === item.to) {
          return;
        }

        // 执行路由跳转，并捕获重复导航错误
        const routePromise = typeof item.to === 'string'
          ? this.$router.push({ name: item.to })
          : this.$router.push(item.to);

        // 捕获并忽略重复导航错误
        routePromise.catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            throw err;
          }
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
// 面包屑导航 - 按钮风格
.breadcrumb-section {
  padding: 16px 28px;
  border-bottom: 1px solid #f0f2f5;
  background: #f9f9f9;

  .breadcrumb-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    .breadcrumb-btn {
      height: 28px;
      padding: 0 12px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 13px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 6px;

      i {
        font-size: 12px;
      }

      // 可点击的面包屑按钮
      &.clickable-btn {
        background: white;
        border: 1px solid #e8dcc0 !important;
        color: #8b7355;

        &:hover {
          background: #fcfaf7 !important;
          border-color: #D7A256 !important;
          color: #D7A256 !important;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(215, 162, 86, 0.15);
        }

        &:active {
          transform: translateY(0);
        }
      }

      // 当前页面按钮（不可点击）
      &.current-btn {
        background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
        border: 1px solid #D7A256 !important;
        color: white !important;
        cursor: default;
        box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);

        &:hover {
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
          border-color: #D7A256 !important;
          color: white !important;
          transform: none !important;
        }

        /deep/ .el-button.is-disabled {
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
          border-color: #D7A256 !important;
          color: white !important;
        }
      }
    }

    .breadcrumb-separator {
      color: #c0c4cc;
      font-size: 12px;
      margin: 0 4px;
      display: flex;
      align-items: center;
      opacity: 0.6;
    }
  }
}
</style>
