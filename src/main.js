import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import _ from "lodash";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

import "./assets/icons/iconfont.css";
import HttpService from "./utils/httpService";
import {kbcPath} from "./utils/globalParam";
import Pagination from "@/components/layouts/Pagination";
import DtPopup from "@/components/layouts/DtPopup";
import QuillEditor from "@/components/layouts/QuillEditor.vue";
import { message } from "./utils/plugin";

Vue.use(HttpService.install);
Vue.use(message);
Vue.component('Pagination', Pagination)
Vue.component('DtPopup', DtPopup)
Vue.component('QuillEditor', QuillEditor)
function loadScript(src) {
  const script = document.createElement('script');
  script.src = src;
  script.async = true; // 异步加载，不阻塞解析
  document.head.appendChild(script);
}
loadScript(`${kbcPath}/vender/dtPublic.js`);

Vue.use(ElementUI, {
  size: "medium",
  zIndex: 3000
});

Vue.directive('hover', {
  bind: function (el, binding, vnode) {
    el.addEventListener(
      "mousemove",
      function () {
        let hoverColor = store.state.layoutStore.themeObj.navTagUnselectedColor;
        let rows = el.getElementsByClassName("el-table__row");
        for (var i = 0; i < rows.length; i++) {
          rows[i].onmouseover = function () {
            let ch = this.children;
            for (let v = 0; v < ch.length; v++) {
              ch[v].style.backgroundColor = hoverColor;
            }
          };
          rows[i].onmouseout = function () {
            let ch = this.children;
            for (let v = 0; v < ch.length; v++) {
              ch[v].style.backgroundColor = "";
            }
          };
        }
      },
      true
    );
  }
});

// 字典翻译
Vue.filter("getDicItemName", function (val, dicType) {
  let dicList = store.state.dicMap[dicType];
  if (dicList) {
    let dicItemObj = _.find(dicList, el => {
      return el.dicItemCode == val
    });
    if (dicItemObj) {
      val = dicItemObj.dicItemName;
      return val;
    }
    return val;
  }
  return val;
})
// 租户名称获取
Vue.filter("getTenantName", function (tenantId) {
  let tenantName = "";
  let tenants = store.state.userTenants;
  if (tenants && tenants.length > 0) {
    let tenant = _.find(tenants, el => {
      return el.tenantId == tenantId
    });
    if (tenant) {
      tenantName = tenant.dicItemName;
    }
  }
  return tenantName;
})


// 用户名称获取
Vue.filter("getNickName", function (userId) {
  if (!localStorage.getItem("tenantUsers")) {
    return userId
  }
  let tenantUsers = JSON.parse(localStorage.getItem("tenantUsers"))
  let userObj = _.find(tenantUsers, el => {
    return el.userId == userId
  })
  if (userObj) {
    return userObj.nickName
  }
  return userId
})

// 用户手机号获取
Vue.filter("getPhone", function (userId) {
  if (!localStorage.getItem("tenantUsers")) {
    return userId
  }
  let tenantUsers = JSON.parse(localStorage.getItem("tenantUsers"))
  let userObj = _.find(tenantUsers, el => {
    return el.userId == userId
  })
  if (userObj) {
    return userObj.phone
  }
  return userId
})


import Auth from "./directives/AuthButton.js";
Vue.use(Auth);

Vue.prototype._ = _;

Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount("#app");
