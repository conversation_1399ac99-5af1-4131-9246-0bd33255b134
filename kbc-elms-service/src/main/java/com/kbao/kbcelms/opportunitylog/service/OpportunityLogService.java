package com.kbao.kbcelms.opportunitylog.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.common.annotation.SyncOpportunityLogTime;
import com.kbao.kbcelms.common.nosql.service.TenantMongoServiceImpl;
import com.kbao.kbcelms.opportunitylog.dao.OpportunityLogDao;
import com.kbao.kbcelms.opportunitylog.model.OpportunityLog;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.IDUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import java.lang.reflect.InvocationTargetException;
import org.springframework.data.mongodb.core.query.Query;


/**
 * 机会日志服务类，提供与机会日志相关的业务逻辑操作，
 * 继承自 BaseMongoServiceImpl 类，可使用其提供的数据库操作方法。
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpportunityLogService extends TenantMongoServiceImpl<OpportunityLog, String, OpportunityLogDao> {
    /**
     * 新增机会日志记录
     * @param opportunityLog 要新增的机会日志对象
     * @return 保存后的机会日志对象
     */
    @SyncOpportunityLogTime
    public OpportunityLog addOpportunityLog(OpportunityLog opportunityLog) {
        // 调用工具类生成流水号并赋值给 logId
        String logId = IDUtils.generateBizId("OPLOG");
        opportunityLog.setId(logId);
        opportunityLog.setCreatorId(BscUserUtils.getUserId());
        opportunityLog.setUpdaterId(BscUserUtils.getUserId());
        opportunityLog.setCreateTime(DateUtils.getCurrentDate());
        opportunityLog.setUpdateTime(DateUtils.getCurrentDate());
        // 调用父类的保存方法将机会日志对象保存到数据库
        return this.save(opportunityLog);
    }

    /**
     * 修改机会日志记录
     * @param opportunityLog 要修改的机会日志对象，包含新的属性值和主键
     * @return 更新后的机会日志对象，若记录不存在则返回 null
     */
    @SyncOpportunityLogTime
    public OpportunityLog updateOpportunityLog(OpportunityLog opportunityLog) throws InvocationTargetException, IllegalAccessException {
        // 获取传入对象的主键
        String primaryKey = opportunityLog.getId();
        if (primaryKey == null) {
            throw new BusinessException("机会日志主键不能为空");
        }

        OpportunityLog existingLog = this.findById(primaryKey);
        if (existingLog == null) {
            throw new BusinessException("机会日志不存在");
        }

        existingLog.setLogDesc(opportunityLog.getLogDesc());
        existingLog.setMeetingDate(opportunityLog.getMeetingDate());
        existingLog.setMeetingContent(opportunityLog.getMeetingContent());
        existingLog.setMeetingImageUrl(opportunityLog.getMeetingImageUrl());
        existingLog.setCustomerDemand(opportunityLog.getCustomerDemand());
        existingLog.setDemandImageUrl(opportunityLog.getDemandImageUrl());
        existingLog.setRecorderName(opportunityLog.getRecorderName());
        existingLog.setUpdaterId(BscUserUtils.getUserId());
        existingLog.setUpdateTime(DateUtils.getCurrentDate());
        this.update(existingLog);
        return existingLog;
    }

    /**
     * 删除机会日志记录
     * @param primaryKey 要删除的机会日志记录的主键
     * @return
     */
    @SyncOpportunityLogTime
    public OpportunityLog deleteOpportunityLog(String primaryKey) {
        OpportunityLog opportunityLog = this.findById(primaryKey);
        if (opportunityLog == null) {
            throw new BusinessException("机会日志不存在");
        }
        this.remove(primaryKey);
        // 确保SyncOpportunityLogTime能从返回值获取opportunityId
        return opportunityLog;
    }

    /**
     * 分页查询机会日志记录
     * @param requestPage 分页请求参数，包含查询条件和分页信息
     * @return 包含机会日志记录列表和分页信息的 Pagination 对象
     */
    public PageInfo<OpportunityLog> pageOpportunityLog(PageRequest<OpportunityLog> requestPage) {
        Query query = new Query();

        if(EmptyUtils.isNotEmpty(requestPage.getParam())){
            // 构建查询条件
            Criteria criteria = Criteria.where("opportunityId").is(requestPage.getParam().getOpportunityId());
            query.addCriteria(criteria);
        }

        // 构建排序条件
        query.with(Sort.by(Sort.Order.asc("createTime")));

        Pagination<OpportunityLog> pagination = new Pagination<>();
        pagination.setPageNum(requestPage.getPageNum());
        pagination.setPageSize(requestPage.getPageSize());

        return this.page(query, pagination);
    }

    /**
     * 根据主键查询机会日志记录
     * @param primaryKey 要查询的机会日志记录的主键
     * @return 查询到的机会日志对象，若记录不存在则返回 null
     */
    public OpportunityLog getOpportunityLog(String primaryKey) {
        return this.findById(primaryKey);
    }

    /**
     * 根据机会ID删除机会日志记录
     * @param opportunityId 机会ID
     */
    @SyncOpportunityLogTime
    public void deleteOpportunityLogByOpportunityId(String opportunityId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("opportunityId").is(opportunityId));
        this.remove(query);
    }

    /**
     * 根据机会ID获取最近更新的一条日志
     * @param opportunityId 机会ID
     * @return 最近更新的一条日志
     */
    public OpportunityLog getLatestOpportunityLogByOpportunityId(String opportunityId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("opportunityId").is(opportunityId));
        query.with(Sort.by(Sort.Order.desc("updateTime")));
        return this.findOne(query);
    }
}
