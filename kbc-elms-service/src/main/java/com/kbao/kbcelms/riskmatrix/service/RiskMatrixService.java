package com.kbao.kbcelms.riskmatrix.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.formula.bean.CategoryScoreResult;
import com.kbao.kbcelms.formula.dto.FormulaCalculationDTO;
import com.kbao.kbcelms.formula.service.FormulaService;
import com.kbao.kbcelms.formula.vo.FormulaCalculationResultVO;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.questionnaire.entity.QuestionnaireAnswer;
import com.kbao.kbcelms.questionnaire.service.QuestionnaireAnswerService;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixQuery;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixRequest;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixResultDTO;
import com.kbao.kbcelms.riskmatrix.dao.RiskMatrixMapper;
import com.kbao.kbcelms.riskmatrix.entity.RiskMatrix;
import com.kbao.kbcelms.riskmatrix.entity.RiskMatrixCategory;
import com.kbao.kbcelms.riskmatrix.entity.RiskMatrixLevel;
import com.kbao.kbcelms.riskmatrix.model.RiskMatrixReport;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixDetailVO;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixVO;
import com.kbao.kbcelms.util.ThreadPoolUtils;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 风险矩阵服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class RiskMatrixService extends BaseSQLServiceImpl<RiskMatrix, Long, RiskMatrixMapper> {

    @Autowired
    private RiskMatrixCategoryService categoryService;
    
    @Autowired
    private RiskMatrixLevelService levelService;

    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;

    @Autowired
    private FormulaService formulaService;

    @Autowired
    private QuestionnaireAnswerService questionnaireAnswerService;

    @Autowired
    private RiskMatrixReportService riskMatrixReportService;

    @Autowired
    private ThreadPoolUtils threadPoolUtils;
    
   
    public PageInfo<RiskMatrixVO> getPage(PageRequest<RiskMatrixQuery> request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<RiskMatrix> list = mapper.selectByQuery(request.getParam());
        PageInfo<RiskMatrix> pageInfo = new PageInfo<>(list);
        
        List<RiskMatrixVO> voList = list.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        PageInfo<RiskMatrixVO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(voList);
        
        return result;
    }
    
   
    public RiskMatrixVO getById(Long id) {
        RiskMatrix entity = mapper.selectByPrimaryKey(id);
        if (entity == null) {
            return null;
        }

        return convertToVO(entity);
    }
    
   
    @Transactional(rollbackFor = Exception.class)
    public void save(RiskMatrixRequest request, String currentUser) {
        RiskMatrix entity = new RiskMatrix();
        BeanUtils.copyProperties(request, entity);
        
        // 处理企业类型
        if (!CollectionUtils.isEmpty(request.getEnterpriseTypes())) {
            entity.setEnterpriseTypes(String.join(",", request.getEnterpriseTypes()));
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        if (request.getId() != null) {
            // 更新
            entity.setUpdateTime(now);
            entity.setUpdateUser(currentUser);
            mapper.updateByPrimaryKeySelective(entity);
        } else {
            // 新增
            entity.setCode(generateCode(request.getName()));
            entity.setStatus(1);
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setCreateUser(currentUser);
            entity.setUpdateUser(currentUser);
            mapper.insertSelective(entity);
        }
    }
    
   
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        // 查询类别信息，用于删除档次配置
        List<RiskMatrixCategory> categories = categoryService.getByMatrixId(id);
        if (!CollectionUtils.isEmpty(categories)) {
            for (RiskMatrixCategory category : categories) {
                // 删除档次配置
                levelService.deleteByCategoryId(category.getId());
            }
        }

        // 删除类别信息
        categoryService.deleteByMatrixId(id);

        // 删除主记录
        return mapper.deleteByPrimaryKey(id) > 0;
    }

    public boolean existsByCode(String code, Long excludeId) {
        RiskMatrix entity = mapper.selectByCode(code);
        if (entity == null) {
            return false;
        }
        return excludeId == null || !excludeId.equals(entity.getId());
    }


    /**
     * 加载风险矩阵报告
     * 重构后的版本：简化逻辑，完善功能，确保数据完整性
     * 
     * @param enterpriseId 企业ID
     * @param dtType 企业类型
     * @return 风险矩阵结果列表
     */
    public List<RiskMatrixResultDTO> loadRisMatrixReport(Long enterpriseId, String dtType) {
        Date calculationStartTime = DateUtils.getCurrentDate();
        log.info("开始加载风险矩阵报告，enterpriseId: {}, enterpriseType: {}", enterpriseId, dtType);
        
        try {
            // 1. 验证输入参数
            validateInputParameters(enterpriseId, dtType);
            
            // 2. 获取风险矩阵配置
            List<RiskMatrixDetailVO> riskMatrixConfigs = getRiskMatrixConfigs(dtType);
            
            // 3. 获取企业答案数据
            List<QuestionnaireAnswer> enterpriseAnswers = getEnterpriseAnswers(enterpriseId, dtType, riskMatrixConfigs);
            
            // 4. 计算风险矩阵结果
            List<RiskMatrixResultDTO> results = calculateRiskMatrixResults(riskMatrixConfigs, enterpriseAnswers);
            
            // 5. 保存完整的风险矩阵报告
            Date calculationEndTime = DateUtils.getCurrentDate();
            saveCompleteRiskMatrixReport(enterpriseId, dtType, results, riskMatrixConfigs, 
                    enterpriseAnswers, calculationStartTime, calculationEndTime);
            
            log.info("风险矩阵报告加载完成，enterpriseId: {}, 结果数量: {}", enterpriseId, results.size());
            return results;
            
        } catch (BusinessException e) {
            log.error("业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("加载风险矩阵报告失败，enterpriseId: {}", enterpriseId, e);
            throw new BusinessException("加载风险矩阵报告失败: " + e.getMessage());
        }
    }

    /**
     * 验证输入参数
     */
    private void validateInputParameters(Long enterpriseId, String dtType) {
        if (enterpriseId == null) {
            throw new BusinessException("企业ID不能为空或无效");
        }
        if (EmptyUtils.isEmpty(dtType)) {
            throw new BusinessException("企业类型不能为空");
        }
    }

    /**
     * 获取企业答案数据
     */
    private List<QuestionnaireAnswer> getEnterpriseAnswers(Long enterpriseId, String dtType, 
            List<RiskMatrixDetailVO> riskMatrixConfigs) {
        // 批量收集所有评分项ID
        List<Long> scoreIds = collectAllScoreIds(riskMatrixConfigs);
        log.info("收集到评分项ID数量: {}", scoreIds.size());

        if (CollectionUtils.isEmpty(scoreIds)) {
            log.warn("未找到任何评分项，无法进行风险评估");
            return new ArrayList<>();
        }

        // 批量查询企业答案
        List<QuestionnaireAnswer> enterpriseAnswers = questionnaireAnswerService
                .getAnswersByScoreIdsAndEnterprise(scoreIds, enterpriseId, dtType);
        log.info("查询到企业答案数量: {}", enterpriseAnswers.size());
        
        return enterpriseAnswers;
    }

    /**
     * 计算风险矩阵结果
     */
    private List<RiskMatrixResultDTO> calculateRiskMatrixResults(List<RiskMatrixDetailVO> riskMatrixConfigs,
            List<QuestionnaireAnswer> enterpriseAnswers) {
        if (CollectionUtils.isEmpty(enterpriseAnswers)) {
            log.warn("企业答案为空，无法进行风险评估");
            return new ArrayList<>();
        }

        // 将答案按评分项ID建立索引，提高查询效率
        Map<Long, QuestionnaireAnswer> answerMap = enterpriseAnswers.stream()
                .filter(Objects::nonNull)
                .filter(answer -> answer.getScoreId() != null)
                .collect(Collectors.toMap(
                        QuestionnaireAnswer::getScoreId,
                        answer -> answer,
                        (existing, replacement) -> existing
                ));

        List<RiskMatrixResultDTO> resultList = new ArrayList<>();

        // 遍历每个风险矩阵
        for (RiskMatrixDetailVO matrix : riskMatrixConfigs) {
            if (matrix == null || CollectionUtils.isEmpty(matrix.getCategories())) {
                continue;
            }

            log.info("开始处理风险矩阵: {}, ID: {}", matrix.getName(), matrix.getId());
            RiskMatrixResultDTO resultDTO = processSingleRiskMatrix(matrix, answerMap);
            resultList.add(resultDTO);
        }

        return resultList;
    }

    /**
     * 处理单个风险矩阵
     */
    private RiskMatrixResultDTO processSingleRiskMatrix(RiskMatrixDetailVO matrix, 
            Map<Long, QuestionnaireAnswer> answerMap) {
        // 创建结果对象
        RiskMatrixResultDTO resultDTO = new RiskMatrixResultDTO();
        resultDTO.setRiskMatrixId(matrix.getId());
        resultDTO.setName(matrix.getName());

        // 并行计算所有类别的得分
        Map<Long, BigDecimal> categoryScoreMap = new HashMap<>();
        List<RiskMatrixDetailVO.CategoryDetailVO> validCategories = new ArrayList<>();
        List<RiskMatrixReport.ScoreItemResult> scoreItemResults = calculateCategoryScores(matrix, answerMap, categoryScoreMap, validCategories);

        // 批量查询档次级别信息
        Map<Long, RiskMatrixLevel> categoryLevelMap = levelService.batchGetLevelsByScore(categoryScoreMap);
        log.info("批量查询档次级别完成，找到 {} 个匹配的档次", categoryLevelMap.size());

        // 构建完整的结果对象
        List<RiskMatrixResultDTO.RiskMatrixLevelVO> levelVOs = buildLevelVOs(validCategories, categoryScoreMap, categoryLevelMap);
        resultDTO.setRiskMatrixLevels(levelVOs);

        // 将计算信息存储到结果对象中，供后续保存使用
        resultDTO.setScoreItemResults(scoreItemResults);

        log.info("风险矩阵 {} 处理完成，包含 {} 个类别结果", matrix.getName(), levelVOs.size());
        return resultDTO;
    }

    /**
     * 计算类别得分
     * 增强版本：记录计算工时和计算过程
     */
    private List<RiskMatrixReport.ScoreItemResult> calculateCategoryScores(RiskMatrixDetailVO matrix, Map<Long, QuestionnaireAnswer> answerMap,
            Map<Long, BigDecimal> categoryScoreMap, List<RiskMatrixDetailVO.CategoryDetailVO> validCategories) {
        
        Date calculationStartTime = DateUtils.getCurrentDate();
        List<RiskMatrixReport.ScoreItemResult> scoreItemResults = new ArrayList<>();
        
        try {
            // 构建并行计算任务
            List<Supplier<CategoryScoreResult>> categoryTasks = matrix.getCategories().stream()
                    .filter(Objects::nonNull)
                    .map(category -> (Supplier<CategoryScoreResult>) () -> {
                        BigDecimal score = calculateCategoryScore(category, answerMap, scoreItemResults);
                        return new CategoryScoreResult(category, score != null ? score : BigDecimal.ZERO);
                    })
                    .collect(Collectors.toList());

            log.info("开始并行计算 {} 个类别的得分", categoryTasks.size());

            // 并行执行计算任务
            List<CategoryScoreResult> categoryResults = threadPoolUtils.executeParallel(
                    categoryTasks,
                    threadPoolUtils.getAsyncExecutor(),
                    60 // 1分钟超时
            );

            // 处理计算结果
            for (CategoryScoreResult result : categoryResults) {
                if (result != null && result.getCategory() != null) {
                    RiskMatrixDetailVO.CategoryDetailVO category = result.getCategory();
                    BigDecimal score = result.getScore();

                    categoryScoreMap.put(category.getId(), score);
                    validCategories.add(category);

                    if (score.compareTo(BigDecimal.ZERO) > 0) {
                        log.info("类别 {} 并行计算完成，最终得分: {}", category.getName(), score);
                    } else {
                        log.warn("类别 {} 计算得分为空或0，使用默认值0", category.getName());
                    }
                }
            }

            Date calculationEndTime = DateUtils.getCurrentDate();
            long totalDuration = calculationEndTime.getTime() - calculationStartTime.getTime();
            
            log.info("并行计算完成，成功计算 {} 个类别得分，总耗时: {}ms", categoryResults.size(), totalDuration);
            
            // 返回计算信息
            return scoreItemResults;

        } catch (Exception e) {
            throw new BusinessException("并行计算类别得分失败", e);
        }
    }

    /**
     * 构建级别VO对象列表
     */
    private List<RiskMatrixResultDTO.RiskMatrixLevelVO> buildLevelVOs(
            List<RiskMatrixDetailVO.CategoryDetailVO> validCategories,
            Map<Long, BigDecimal> categoryScoreMap,
            Map<Long, RiskMatrixLevel> categoryLevelMap) {
        
        List<RiskMatrixResultDTO.RiskMatrixLevelVO> levelVOs = new ArrayList<>();
        
        for (RiskMatrixDetailVO.CategoryDetailVO category : validCategories) {
            BigDecimal score = categoryScoreMap.get(category.getId());
            RiskMatrixLevel level = categoryLevelMap.get(category.getId());

            RiskMatrixResultDTO.RiskMatrixLevelVO levelVO = buildLevelVO(category, score, level);
            levelVOs.add(levelVO);
        }
        
        // 按照sortOrder升序排序
        levelVOs.sort(Comparator.comparing(RiskMatrixResultDTO.RiskMatrixLevelVO::getSortOrder, 
                Comparator.nullsLast(Comparator.naturalOrder())));
        
        return levelVOs;
    }

    /**
     * 保存完整的风险矩阵报告
     */
    private void saveCompleteRiskMatrixReport(Long enterpriseId, String enterpriseType,
            List<RiskMatrixResultDTO> results,
            List<RiskMatrixDetailVO> riskMatrixDetailVOs,
            List<QuestionnaireAnswer> enterpriseAnswers,
            Date calculationStartTime, Date calculationEndTime) {
        try {
            log.info("开始保存完整的风险矩阵报告，enterpriseId: {}", enterpriseId);

            // 构建完整的报告对象
            RiskMatrixReport report = buildCompleteRiskMatrixReport(
                    enterpriseId, enterpriseType, results, riskMatrixDetailVOs, 
                    enterpriseAnswers, calculationStartTime, calculationEndTime);

            // 保存报告
            riskMatrixReportService.saveReport(report);

            log.info("风险矩阵报告保存成功，enterpriseId: {}, reportId: {}", enterpriseId, report.getId());

        } catch (Exception e) {
            log.error("保存风险矩阵报告失败，enterpriseId: {}", enterpriseId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 构建完整的风险矩阵报告
     */
    private RiskMatrixReport buildCompleteRiskMatrixReport(Long enterpriseId, String enterpriseType,
            List<RiskMatrixResultDTO> results,
            List<RiskMatrixDetailVO> riskMatrixDetailVOs,
            List<QuestionnaireAnswer> enterpriseAnswers,
            Date calculationStartTime, Date calculationEndTime) {

        // 获取企业名称
        String enterpriseName = getEnterpriseName(enterpriseId.intValue());

        // 计算总分和平均分
        BigDecimal totalScore = calculateTotalScore(results);
        BigDecimal averageScore = calculateAverageScore(results);

        // 确定整体风险等级
        String overallRiskLevel = determineOverallRiskLevel(results);

        // 构建类别结果列表
        List<RiskMatrixReport.CategoryResult> categoryResults = buildCategoryResults(results);

        // 构建计算过程详情
        RiskMatrixReport.CalculationProcess calculationProcess = buildCalculationProcess(
                enterpriseAnswers, results, riskMatrixDetailVOs, enterpriseType);

        // 构建雷达图数据
        List<RiskMatrixResultDTO> radarChartDatas = buildRadarChartData(results);

        // 计算耗时
        long calculationDuration = calculationEndTime.getTime() - calculationStartTime.getTime();

        // 构建报告实体
        return RiskMatrixReport.builder()
                .enterpriseId(enterpriseId)
                .enterpriseName(enterpriseName)
                .enterpriseType(enterpriseType)
                .reportStatus("COMPLETED")
                .reportVersion("1.0")
                .totalScore(totalScore)
                .averageScore(averageScore)
                .overallRiskLevel(overallRiskLevel)
                .categoryResults(categoryResults)
                .calculationProcess(calculationProcess)
                .radarChartDatas(radarChartDatas)
                .calculationStartTime(calculationStartTime)
                .calculationEndTime(calculationEndTime)
                .calculationDuration(calculationDuration)
                .build();
    }

    /**
     * 获取企业类型
     */
    private String getEnterpriseType(Integer enterpriseId) {
        GenAgentEnterprise genAgentEnterprise = genAgentEnterpriseService.selectByPrimaryKey(enterpriseId);
        if (EmptyUtils.isEmpty(genAgentEnterprise)) {
            throw new BusinessException("企业不存在，enterpriseId: " + enterpriseId);
        }
        String dtType = genAgentEnterprise.getDtType();
        if (EmptyUtils.isEmpty(dtType)) {
            throw new BusinessException("企业类型不能为空，enterpriseId: " + enterpriseId);
        }
        return dtType;
    }

    /**
     * 获取风险矩阵配置
     */
    private List<RiskMatrixDetailVO> getRiskMatrixConfigs(String dtType) {
        List<RiskMatrixDetailVO> riskMatrixDetailVOs = mapper.selectByEnterpriseTypes(dtType);
        if (EmptyUtils.isEmpty(riskMatrixDetailVOs)) {
            throw new BusinessException("未找到对应企业类型的风险矩阵配置，dtType: " + dtType);
        }
        return riskMatrixDetailVOs;
    }

    /**
     * 批量收集所有评分项ID
     */
    private List<Long> collectAllScoreIds(List<RiskMatrixDetailVO> riskMatrixDetailVOs) {
        return riskMatrixDetailVOs.stream()
                .filter(Objects::nonNull)
                .flatMap(matrix -> {
                    if (CollectionUtils.isEmpty(matrix.getCategories())) {
                        return Stream.empty();
                    }
                    return matrix.getCategories().stream();
                })
                .filter(Objects::nonNull)
                .flatMap(category -> {
                    if (CollectionUtils.isEmpty(category.getScoreItems())) {
                        return Stream.empty();
                    }
                    return category.getScoreItems().stream();
                })
                .filter(Objects::nonNull)
                .map(RiskMatrixDetailVO.ScoreItemDetailVO::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 构建档次级别VO对象
     */
    private RiskMatrixResultDTO.RiskMatrixLevelVO buildLevelVO(RiskMatrixDetailVO.CategoryDetailVO category,
                                                               BigDecimal score,
                                                               RiskMatrixLevel level) {
        RiskMatrixResultDTO.RiskMatrixLevelVO.RiskMatrixLevelVOBuilder builder =
                RiskMatrixResultDTO.RiskMatrixLevelVO.builder()
                        .calculationMethod(category.getCalculationMethod())
                        .categoryId(category.getId())
                        .categoryName(category.getName())
                        .score(score != null ? score : BigDecimal.ZERO)
                        .scoreIds(category.getScoreItems().stream().map(RiskMatrixDetailVO.ScoreItemDetailVO::getId).collect(Collectors.toList()))
                        .sortOrder(category.getSortOrder())
                        .description(category.getDescription());

        if (level != null) {
            // 填充档次信息
            builder.levelId(level.getId())
                   .levelName(level.getName())
                   .levelDescription(level.getDescription())
                   .scoreRange(formatScoreRange(level.getMinValue(), level.getMaxValue()));

            log.debug("类别 {} 匹配到档次: {}, 分数范围: [{}, {}]",
                     category.getName(), level.getName(), level.getMinValue(), level.getMaxValue());
        }
        return builder.build();
    }

    /**
     * 格式化分数范围显示
     */
    private String formatScoreRange(BigDecimal minValue, BigDecimal maxValue) {
        if (minValue == null || maxValue == null) {
            return "N/A";
        }
        return String.format("[%.2f, %.2f]", minValue.doubleValue(), maxValue.doubleValue());
    }

    /**
     * 计算类别得分
     */
    private BigDecimal calculateCategoryScore(RiskMatrixDetailVO.CategoryDetailVO category,
                                              Map<Long, QuestionnaireAnswer> answerMap,
                                              List<RiskMatrixReport.ScoreItemResult> scoreItemResults) {
        if (CollectionUtils.isEmpty(category.getScoreItems())) {
            log.warn("类别 {} 没有评分项，跳过计算", category.getName());
            return null;
        }

        List<BigDecimal> itemScores = new ArrayList<>();

        // 计算每个评分项的得分
        for (RiskMatrixDetailVO.ScoreItemDetailVO item : category.getScoreItems()) {
            if (item == null || item.getId() == null) {
                continue;
            }
            RiskMatrixReport.ScoreItemResult scoreItemResult = new RiskMatrixReport.ScoreItemResult();
            BigDecimal itemScore = calculateItemScore(item, answerMap, scoreItemResult);
            if (itemScore != null) {
                itemScores.add(itemScore);
            }
            // 评分项信息
            scoreItemResult.setScoreItemId(item.getId());
            scoreItemResult.setScoreItemName(item.getName());
            scoreItemResult.setWeight(item.getWeight());
            scoreItemResult.setFinalScore(itemScore);
            scoreItemResults.add(scoreItemResult);
        }

        if (itemScores.isEmpty()) {
            log.warn("类别 {} 没有有效的评分项得分，跳过计算", category.getName());
            return null;
        }
        // 根据计算方法汇总得分
        return aggregateScores(itemScores, category.getCalculationMethod());
    }

    /**
     * 计算单个评分项得分
     */
    private BigDecimal calculateItemScore(RiskMatrixDetailVO.ScoreItemDetailVO item,
                                          Map<Long, QuestionnaireAnswer> answerMap,
                                          RiskMatrixReport.ScoreItemResult scoreItemResult) {
        QuestionnaireAnswer answer = answerMap.get(item.getId());
        if (answer == null || answer.getScore() == null) {
            log.debug("评分项 {} 没有找到对应答案，跳过", item.getName());
            return null;
        }
        log.info("评分项 {} 得分: {}", item.getName(), answer.getScore());

        BigDecimal baseScore = answer.getScore();
        scoreItemResult.setQuestionnaireScore(answer.getScore());

        // 如果需要应用公式
        if (YesNoEnum.YES.getValue().equals(item.getIsFormula()) && item.getFormulaId() != null) {
            try {
                FormulaCalculationDTO calculationDTO = new FormulaCalculationDTO();
                calculationDTO.setFormulaId(item.getFormulaId());
                calculationDTO.setScore(answer.getScore());

                FormulaCalculationResultVO resultVO = formulaService.calculateFormula(calculationDTO);
                if (resultVO != null && resultVO.getResult() != null) {
                    baseScore = resultVO.getResult();
                    scoreItemResult.setFormulaName(resultVO.getFormulaName());
                    scoreItemResult.setFormula(resultVO.getFormula());
                    scoreItemResult.setCalculatedScore(baseScore);
                    log.debug("评分项 {} 应用公式后得分: {}", item.getName(), baseScore);
                }
            } catch (Exception e) {
                log.error("计算公式失败，评分项: {}, 公式ID: {}", item.getName(), item.getFormulaId(), e);
            }
        }

        // 应用权重系数
        if (item.getCoefficient() != null && item.getCoefficient().compareTo(BigDecimal.ZERO) > 0) {
            baseScore = baseScore.multiply(item.getCoefficient());
            scoreItemResult.setCoefficient(item.getCoefficient());
        }

        return baseScore;
    }

    /**
     * 根据计算方法汇总得分
     */
    private BigDecimal aggregateScores(List<BigDecimal> scores, String calculationMethod) {
        if (CollectionUtils.isEmpty(scores)) {
            return BigDecimal.ZERO;
        }

        switch (StringUtils.hasText(calculationMethod) ? calculationMethod.toLowerCase() : "sum") {
            case "sum":
                return scores.stream()
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

            case "sumandavg":
                return scores.stream()
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(new BigDecimal(scores.size()), 2, RoundingMode.HALF_UP);

            case "avg":
                BigDecimal sum = scores.stream()
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                return sum.divide(new BigDecimal(scores.size()), 2, RoundingMode.HALF_UP);

            case "max":
                return scores.stream()
                        .filter(Objects::nonNull)
                        .max(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);

            case "min":
                return scores.stream()
                        .filter(Objects::nonNull)
                        .min(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);

            default:
                log.warn("未知的计算方法: {}，使用默认求和方式", calculationMethod);
                return scores.stream()
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }

    /**
     * 转换为VO对象
     */
    private RiskMatrixVO convertToVO(RiskMatrix entity) {
        RiskMatrixVO vo = new RiskMatrixVO();
        BeanUtils.copyProperties(entity, vo);
        
        // 处理企业类型
        if (StringUtils.hasText(entity.getEnterpriseTypes())) {
            vo.setEnterpriseTypes(Arrays.asList(entity.getEnterpriseTypes().split(",")));
            vo.setEnterpriseTypesDisplay(entity.getEnterpriseTypes().replace(",", "、"));
        }
        
        // 处理状态名称
        vo.setStatusName(entity.getStatus() == 1 ? "启用" : "禁用");
        
        return vo;
    }

    /**
     * 生成编码
     */
    private String generateCode(String name) {
        // 简单的编码生成逻辑，实际项目中可能需要更复杂的规则
        return name.toUpperCase().replaceAll("[\\s\\u4e00-\\u9fa5]", "_") + "_" + System.currentTimeMillis();
    }

    /**
     * 获取企业名称
     */
    private String getEnterpriseName(Integer enterpriseId) {
        try {
            GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(enterpriseId);
            return enterprise != null ? enterprise.getName() : "未知企业";
        } catch (Exception e) {
            log.warn("获取企业名称失败，enterpriseId: {}", enterpriseId, e);
            return "未知企业";
        }
    }

    /**
     * 计算总分
     */
    private BigDecimal calculateTotalScore(List<RiskMatrixResultDTO> results) {
        return results.stream()
                .flatMap(result -> result.getRiskMatrixLevels().stream())
                .map(RiskMatrixResultDTO.RiskMatrixLevelVO::getScore)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算平均分
     */
    private BigDecimal calculateAverageScore(List<RiskMatrixResultDTO> results) {
        List<BigDecimal> scores = results.stream()
                .flatMap(result -> result.getRiskMatrixLevels().stream())
                .map(RiskMatrixResultDTO.RiskMatrixLevelVO::getScore)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (scores.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal sum = scores.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        return sum.divide(BigDecimal.valueOf(scores.size()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 确定整体风险等级
     * 优化版本：综合考虑平均分和类别风险分布
     */
    private String determineOverallRiskLevel(List<RiskMatrixResultDTO> results) {
        if (CollectionUtils.isEmpty(results)) {
            return "未知风险";
        }
        
        BigDecimal averageScore = calculateAverageScore(results);
        
        // 统计高风险类别数量
        int highRiskCategoryCount = 0;
        int totalCategoryCount = 0;
        
        for (RiskMatrixResultDTO result : results) {
            if (result.getRiskMatrixLevels() != null) {
                for (RiskMatrixResultDTO.RiskMatrixLevelVO levelVO : result.getRiskMatrixLevels()) {
                    totalCategoryCount++;
                    if (levelVO.getScore() != null && levelVO.getScore().compareTo(BigDecimal.valueOf(60)) < 0) {
                        highRiskCategoryCount++;
                    }
                }
            }
        }
        
        // 综合评分和风险分布确定风险等级
        if (averageScore.compareTo(BigDecimal.valueOf(80)) >= 0 && highRiskCategoryCount == 0) {
            return "低风险";
        } else if (averageScore.compareTo(BigDecimal.valueOf(70)) >= 0 && 
                   (double) highRiskCategoryCount / totalCategoryCount <= 0.2) {
            return "中风险";
        } else if (averageScore.compareTo(BigDecimal.valueOf(50)) >= 0 && 
                   (double) highRiskCategoryCount / totalCategoryCount <= 0.5) {
            return "高风险";
        } else {
            return "极高风险";
        }
    }

        /**
     * 构建类别结果列表
     */
    private List<RiskMatrixReport.CategoryResult> buildCategoryResults(List<RiskMatrixResultDTO> results) {
        List<RiskMatrixReport.CategoryResult> categoryResults = new ArrayList<>();
        for (RiskMatrixResultDTO result : results) {
            if (result.getRiskMatrixLevels() != null) {
                for (RiskMatrixResultDTO.RiskMatrixLevelVO levelVO : result.getRiskMatrixLevels()) {
                    RiskMatrixReport.CategoryResult categoryResult = RiskMatrixReport.CategoryResult.builder()
                            .riskMatrixId(result.getRiskMatrixId())
                            .riskMatrixName(result.getName())
                            .categoryId(levelVO.getCategoryId())
                            .categoryName(levelVO.getCategoryName())
                            .categoryDescription(levelVO.getDescription())
                            .calculationMethod(levelVO.getCalculationMethod())
                            .categoryScore(levelVO.getScore())
                            .level(buildLevelInfo(levelVO))
                            .scoreIds(levelVO.getScoreIds())
                            .build();
                    categoryResults.add(categoryResult);
                }
            }
        }

        return categoryResults;
    }

    /**
     * 构建档次信息
     */
    private RiskMatrixReport.LevelInfo buildLevelInfo(RiskMatrixResultDTO.RiskMatrixLevelVO levelVO) {
        return RiskMatrixReport.LevelInfo.builder()
                .levelId(levelVO.getLevelId())
                .name(levelVO.getLevelName())
                .description(levelVO.getLevelDescription())
                .scoreRange(levelVO.getScoreRange())
                .build();
    }

    /**
     * 构建计算过程详情
     */
    private RiskMatrixReport.CalculationProcess buildCalculationProcess(
            List<QuestionnaireAnswer> enterpriseAnswers,
            List<RiskMatrixResultDTO> results,
            List<RiskMatrixDetailVO> riskMatrixDetailVOs,
            String enterpriseType) {

        // 构建风险矩阵信息
        RiskMatrixReport.RiskMatrixInfo riskMatrixInfo = null;
        if (!riskMatrixDetailVOs.isEmpty()) {
            RiskMatrixDetailVO firstMatrix = riskMatrixDetailVOs.get(0);
            riskMatrixInfo = RiskMatrixReport.RiskMatrixInfo.builder()
                    .matrixId(firstMatrix.getId())
                    .matrixName(firstMatrix.getName())
                    .matrixDescription(firstMatrix.getDescription())
                    .enterpriseTypes(Collections.singletonList(enterpriseType))
                    .build();
        }

        // 构建问卷结果
        List<RiskMatrixReport.QuestionnaireAnswerDetail> details = new ArrayList<>();
        for (QuestionnaireAnswer answer : enterpriseAnswers) {
            RiskMatrixReport.QuestionnaireAnswerDetail answerDetail = RiskMatrixReport.QuestionnaireAnswerDetail.builder()
                    .questionId(answer.getQuestionId())
                    .questionTitle(answer.getQuestionTitle())
                    .scoreId(answer.getScoreId())
                    .answerContent(answer.getAnswerContent())
                    .optionValue(answer.getOptionValue())
                    .score(answer.getScore())
                    .build();
            details.add(answerDetail);
        }

        Set<RiskMatrixReport.ScoreItemResult> scoreItemResultSet = new HashSet<>();
        // 构建评分项计算结果
        for (RiskMatrixResultDTO resultDTO:results){
            scoreItemResultSet.addAll(resultDTO.getScoreItemResults());
        }

        return RiskMatrixReport.CalculationProcess.builder()
                .questionnaireAnswers(details)
                .scoreItemResults(new ArrayList<>(scoreItemResultSet))
//                .riskMatrix(riskMatrixInfo)
                .build();
    }


    /**
     * 构建雷达图数据
     * 优化版本：为每个类别生成标准化的雷达图数据
     */
    private List<RiskMatrixResultDTO> buildRadarChartData(List<RiskMatrixResultDTO> results) {
        if (results.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 为雷达图创建标准化的数据结构
        List<RiskMatrixResultDTO> radarData = new ArrayList<>();
        
        for (RiskMatrixResultDTO result : results) {
            if (result.getRiskMatrixLevels() != null && !result.getRiskMatrixLevels().isEmpty()) {
                // 创建雷达图专用的结果对象
                RiskMatrixResultDTO radarResult = new RiskMatrixResultDTO();
                radarResult.setRiskMatrixId(result.getRiskMatrixId());
                radarResult.setName(result.getName());
                radarResult.setRiskMatrixLevels(result.getRiskMatrixLevels());
                radarData.add(radarResult);
            }
        }
        
        log.info("构建雷达图数据完成，共 {} 个矩阵", radarData.size());
        return radarData;
    }
}