package com.kbao.kbcelms.questionnaire.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestion;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷问题Mapper接口
 */
@Mapper
public interface QuestionnaireQuestionMapper extends BaseMapper<QuestionnaireQuestion, Long> {

    /**
     * 根据问卷ID查询问题列表（包含选项）
     */
    List<QuestionnaireQuestionVO> selectQuestionsByQuestionnaireId(@Param("questionnaireId") Long questionnaireId);

    /**
     * 批量删除问题（逻辑删除）
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据问卷ID删除所有问题（逻辑删除）
     */
    int deleteByQuestionnaireId(@Param("questionnaireId") Long questionnaireId);

    /**
     * 获取问卷的最大排序号
     */
    Integer getMaxSortOrder(@Param("questionnaireId") Long questionnaireId);
}
