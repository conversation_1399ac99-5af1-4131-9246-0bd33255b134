package com.kbao.kbcelms.questionnaire.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireQuestionOptionSaveBean;
import com.kbao.kbcelms.questionnaire.dao.QuestionnaireQuestionOptionMapper;
import com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestionOption;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionOptionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 问卷问题选项服务实现类
 */
@Slf4j
@Service
public class QuestionnaireQuestionOptionService extends BaseSQLServiceImpl<QuestionnaireQuestionOption, Long, QuestionnaireQuestionOptionMapper> {

    /**
     * 根据问题ID查询选项列表
     */
    public List<QuestionnaireQuestionOptionVO> getOptionsByQuestionId(Long questionId) {
        try {
            if (questionId == null) {
                throw new IllegalArgumentException("问题ID不能为空");
            }

            return this.mapper.selectOptionsByQuestionId(questionId);
        } catch (Exception e) {
            log.error("查询选项列表失败，questionId: {}", questionId, e);
            throw new RuntimeException("查询失败");
        }
    }

    /**
     * 批量删除选项（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteByIds(List<Long> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return true;
            }

            int result = this.mapper.batchDeleteByIds(ids);
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除选项失败，ids: {}", ids, e);
            throw new RuntimeException("删除失败");
        }
    }

    /**
     * 根据问题ID删除所有选项（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByQuestionId(Long questionId) {
        try {
            if (questionId == null) {
                throw new IllegalArgumentException("问题ID不能为空");
            }

            int result = this.mapper.deleteByQuestionId(questionId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除问题选项失败，questionId: {}", questionId, e);
            throw new RuntimeException("删除失败");
        }
    }

    /**
     * 根据问题ID列表删除所有选项（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByQuestionIds(List<Long> questionIds) {
        try {
            if (CollectionUtils.isEmpty(questionIds)) {
                return true;
            }

            int result = this.mapper.deleteByQuestionIds(questionIds);
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除问题选项失败，questionIds: {}", questionIds, e);
            throw new RuntimeException("删除失败");
        }
    }

    /**
     * 获取问题的最大排序号
     */
    public Integer getMaxSortOrder(Long questionId) {
        try {
            if (questionId == null) {
                throw new IllegalArgumentException("问题ID不能为空");
            }

            return this.mapper.getMaxSortOrder(questionId);
        } catch (Exception e) {
            log.error("获取最大排序号失败，questionId: {}", questionId, e);
            throw new RuntimeException("查询失败");
        }
    }

    /**
     * 保存选项列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOptions(Long questionId, List<QuestionnaireQuestionOptionSaveBean> options) {
        if (CollectionUtils.isEmpty(options)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 0; i < options.size(); i++) {
            QuestionnaireQuestionOptionSaveBean optionBean = options.get(i);
            
            QuestionnaireQuestionOption option = new QuestionnaireQuestionOption();
            BeanUtils.copyProperties(optionBean, option);
            option.setQuestionId(questionId);
            option.setSortOrder(i + 1);
            option.setCreateTime(now);
            option.setUpdateTime(now);
            option.setDeleted(0);
            
            if (optionBean.getId() == null) {
                this.mapper.insert(option);
            } else {
                this.updateByPrimaryKeySelective(option);
            }
        }
    }

    /**
     * 保存单个选项
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveOption(QuestionnaireQuestionOptionSaveBean optionBean) {
        try {
            if (optionBean == null) {
                throw new IllegalArgumentException("选项参数不能为空");
            }
            if (optionBean.getQuestionId() == null) {
                throw new IllegalArgumentException("问题ID不能为空");
            }

            QuestionnaireQuestionOption option = new QuestionnaireQuestionOption();
            BeanUtils.copyProperties(optionBean, option);
            
            LocalDateTime now = LocalDateTime.now();
            
            if (optionBean.getId() == null) {
                // 新增
                option.setCreateTime(now);
                option.setUpdateTime(now);
                option.setDeleted(0);
                
                // 设置排序号
                if (option.getSortOrder() == null) {
                    Integer maxSortOrder = getMaxSortOrder(option.getQuestionId());
                    option.setSortOrder(maxSortOrder == null ? 1 : maxSortOrder + 1);
                }
                
                this.mapper.insert(option);
            } else {
                // 编辑
                option.setUpdateTime(now);
                this.updateByPrimaryKeySelective(option);
            }

            return option.getId();
        } catch (Exception e) {
            log.error("保存选项失败", e);
            throw new RuntimeException("保存失败");
        }
    }

    /**
     * 删除选项
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOption(Long id) {
        try {
            if (id == null) {
                throw new IllegalArgumentException("选项ID不能为空");
            }

            // 逻辑删除选项
            QuestionnaireQuestionOption option = new QuestionnaireQuestionOption();
            option.setId(id);
            option.setDeleted(1);
            option.setUpdateTime(LocalDateTime.now());
            
            int result = this.updateByPrimaryKeySelective(option);
            return result > 0;
        } catch (Exception e) {
            log.error("删除选项失败，id: {}", id, e);
            throw new RuntimeException("删除失败");
        }
    }
}
