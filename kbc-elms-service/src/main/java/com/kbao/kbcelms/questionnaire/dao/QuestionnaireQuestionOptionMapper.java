package com.kbao.kbcelms.questionnaire.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.questionnaire.entity.QuestionnaireQuestionOption;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionOptionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷问题选项Mapper接口
 */
public interface QuestionnaireQuestionOptionMapper extends BaseMapper<QuestionnaireQuestionOption, Long> {

    /**
     * 根据问题ID查询选项列表
     */
    List<QuestionnaireQuestionOptionVO> selectOptionsByQuestionId(@Param("questionId") Long questionId);

    /**
     * 批量删除选项（逻辑删除）
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据问题ID删除所有选项（逻辑删除）
     */
    int deleteByQuestionId(@Param("questionId") Long questionId);

    /**
     * 根据问题ID列表删除所有选项（逻辑删除）
     */
    int deleteByQuestionIds(@Param("questionIds") List<Long> questionIds);

    /**
     * 获取问题的最大排序号
     */
    Integer getMaxSortOrder(@Param("questionId") Long questionId);
}
