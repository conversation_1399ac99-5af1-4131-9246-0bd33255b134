package com.kbao.kbcelms.util;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.kbao.tool.util.EmptyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * PDF工具类
 * 提供PDF生成和下载功能
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class PdfUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(PdfUtils.class);
    
    // 默认字体大小
    private static final int DEFAULT_FONT_SIZE = 12;
    private static final int TITLE_FONT_SIZE = 18;
    private static final int HEADER_FONT_SIZE = 14;
    
    // 中文字体路径（需要根据实际情况调整）
    private static final String CHINESE_FONT_PATH = "STSong-Light";
    
    /**
     * 创建PDF文档
     * 
     * @param outputStream 输出流
     * @param title 文档标题
     * @param content 文档内容
     * @throws Exception
     */
    public static void createPdf(OutputStream outputStream, String title, String content) throws Exception {
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, outputStream);
        
        document.open();
        
        // 设置中文字体
        BaseFont baseFont = BaseFont.createFont(CHINESE_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font titleFont = new Font(baseFont, TITLE_FONT_SIZE, Font.BOLD);
        Font contentFont = new Font(baseFont, DEFAULT_FONT_SIZE, Font.NORMAL);
        
        // 添加标题
        if (!EmptyUtils.isEmpty(title)) {
            Paragraph titleParagraph = new Paragraph(title, titleFont);
            titleParagraph.setAlignment(Element.ALIGN_CENTER);
            titleParagraph.setSpacingAfter(20);
            document.add(titleParagraph);
        }
        
        // 添加内容
        if (!EmptyUtils.isEmpty(content)) {
            Paragraph contentParagraph = new Paragraph(content, contentFont);
            contentParagraph.setAlignment(Element.ALIGN_LEFT);
            document.add(contentParagraph);
        }
        
        document.close();
    }
    
    /**
     * 创建包含表格的PDF文档
     * 
     * @param outputStream 输出流
     * @param title 文档标题
     * @param headers 表格头部
     * @param data 表格数据
     * @throws Exception
     */
    public static void createPdfWithTable(OutputStream outputStream, String title, 
                                         List<String> headers, List<List<String>> data) throws Exception {
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, outputStream);
        
        document.open();
        
        // 设置中文字体
        BaseFont baseFont = BaseFont.createFont(CHINESE_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font titleFont = new Font(baseFont, TITLE_FONT_SIZE, Font.BOLD);
        Font headerFont = new Font(baseFont, HEADER_FONT_SIZE, Font.BOLD);
        Font cellFont = new Font(baseFont, DEFAULT_FONT_SIZE, Font.NORMAL);
        
        // 添加标题
        if (!EmptyUtils.isEmpty(title)) {
            Paragraph titleParagraph = new Paragraph(title, titleFont);
            titleParagraph.setAlignment(Element.ALIGN_CENTER);
            titleParagraph.setSpacingAfter(20);
            document.add(titleParagraph);
        }
        
        // 创建表格
        if (headers != null && !headers.isEmpty()) {
            PdfPTable table = new PdfPTable(headers.size());
            table.setWidthPercentage(100);
            table.setSpacingBefore(10);
            
            // 添加表头
            for (String header : headers) {
                PdfPCell headerCell = new PdfPCell(new Phrase(header, headerFont));
                headerCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                headerCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                headerCell.setPadding(8);
                table.addCell(headerCell);
            }
            
            // 添加数据行
            if (data != null) {
                for (List<String> row : data) {
                    for (String cell : row) {
                        PdfPCell dataCell = new PdfPCell(new Phrase(cell != null ? cell : "", cellFont));
                        dataCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                        dataCell.setPadding(5);
                        table.addCell(dataCell);
                    }
                }
            }
            
            document.add(table);
        }
        
        document.close();
    }
    
    /**
     * 创建复杂PDF文档（支持多种内容类型）
     * 
     * @param outputStream 输出流
     * @param pdfContent PDF内容配置
     * @throws Exception
     */
    public static void createComplexPdf(OutputStream outputStream, PdfContent pdfContent) throws Exception {
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, outputStream);
        
        document.open();
        
        // 设置中文字体
        BaseFont baseFont = BaseFont.createFont(CHINESE_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font titleFont = new Font(baseFont, TITLE_FONT_SIZE, Font.BOLD);
        Font headerFont = new Font(baseFont, HEADER_FONT_SIZE, Font.BOLD);
        Font contentFont = new Font(baseFont, DEFAULT_FONT_SIZE, Font.NORMAL);
        
        // 添加标题
        if (!EmptyUtils.isEmpty(pdfContent.getTitle())) {
            Paragraph titleParagraph = new Paragraph(pdfContent.getTitle(), titleFont);
            titleParagraph.setAlignment(Element.ALIGN_CENTER);
            titleParagraph.setSpacingAfter(20);
            document.add(titleParagraph);
        }
        
        // 添加各个章节
        if (pdfContent.getSections() != null) {
            for (PdfSection section : pdfContent.getSections()) {
                // 章节标题
                if (!EmptyUtils.isEmpty(section.getSectionTitle())) {
                    Paragraph sectionTitle = new Paragraph(section.getSectionTitle(), headerFont);
                    sectionTitle.setSpacingBefore(15);
                    sectionTitle.setSpacingAfter(10);
                    document.add(sectionTitle);
                }
                
                // 章节内容
                if (!EmptyUtils.isEmpty(section.getContent())) {
                    Paragraph content = new Paragraph(section.getContent(), contentFont);
                    content.setSpacingAfter(10);
                    document.add(content);
                }
                
                // 章节表格
                if (section.getTableData() != null && !section.getTableData().isEmpty()) {
                    addTableToDocument(document, section.getTableHeaders(), 
                                     section.getTableData(), headerFont, contentFont);
                }
            }
        }
        
        document.close();
    }
    
    /**
     * 下载PDF文件
     * 
     * @param response HTTP响应
     * @param fileName 文件名
     * @param pdfData PDF数据
     */
    public static void downloadPdf(HttpServletResponse response, String fileName, byte[] pdfData) {
        try {
            // 设置响应头
            response.setContentType("application/pdf");
            response.setCharacterEncoding("UTF-8");
            
            // 处理文件名编码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setContentLength(pdfData.length);
            
            // 写入响应流
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(pdfData);
            outputStream.flush();
            outputStream.close();
            
        } catch (Exception e) {
            logger.error("PDF下载失败: {}", e.getMessage(), e);
            throw new RuntimeException("PDF下载失败", e);
        }
    }
    
    /**
     * 生成PDF字节数组
     * 
     * @param title 标题
     * @param content 内容
     * @return PDF字节数组
     */
    public static byte[] generatePdfBytes(String title, String content) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            createPdf(outputStream, title, content);
            return outputStream.toByteArray();
        } catch (Exception e) {
            logger.error("生成PDF字节数组失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成PDF失败", e);
        }
    }
    
    /**
     * 生成包含表格的PDF字节数组
     * 
     * @param title 标题
     * @param headers 表格头部
     * @param data 表格数据
     * @return PDF字节数组
     */
    public static byte[] generatePdfBytesWithTable(String title, List<String> headers, List<List<String>> data) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            createPdfWithTable(outputStream, title, headers, data);
            return outputStream.toByteArray();
        } catch (Exception e) {
            logger.error("生成包含表格的PDF字节数组失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成PDF失败", e);
        }
    }
    
    /**
     * 生成复杂PDF字节数组
     *
     * @param pdfContent PDF内容配置
     * @return PDF字节数组
     */
    public static byte[] generateComplexPdfBytes(PdfContent pdfContent) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            createComplexPdf(outputStream, pdfContent);
            return outputStream.toByteArray();
        } catch (Exception e) {
            logger.error("生成复杂PDF字节数组失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成PDF失败", e);
        }
    }

    /**
     * 生成复杂PDF并返回MultipartFile
     *
     * @param pdfContent PDF内容配置
     * @param fileName 文件名（可选，如果为空则自动生成）
     * @return MultipartFile对象
     */
    public static MultipartFile generateComplexPdfMultipartFile(PdfContent pdfContent, String fileName) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            createComplexPdf(outputStream, pdfContent);
            byte[] pdfBytes = outputStream.toByteArray();

            // 如果没有提供文件名，则自动生成
            if (EmptyUtils.isEmpty(fileName)) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
                String timestamp = dateFormat.format(new Date());
                fileName = "PDF报告_" + timestamp + ".pdf";
            }

            // 确保文件名以.pdf结尾
            if (!fileName.toLowerCase().endsWith(".pdf")) {
                fileName += ".pdf";
            }

            // 创建MockMultipartFile对象
            return new MockMultipartFile(
                "file",                    // 参数名
                fileName,                  // 原始文件名
                "application/pdf",         // 内容类型
                pdfBytes                   // 文件内容
            );

        } catch (Exception e) {
            logger.error("生成复杂PDF MultipartFile失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成PDF失败", e);
        }
    }

    /**
     * 生成复杂PDF并返回MultipartFile（使用默认文件名）
     *
     * @param pdfContent PDF内容配置
     * @return MultipartFile对象
     */
    public static MultipartFile generateComplexPdfMultipartFile(PdfContent pdfContent) {
        return generateComplexPdfMultipartFile(pdfContent, null);
    }
    
    /**
     * 向文档添加表格
     */
    private static void addTableToDocument(Document document, List<String> headers, 
                                         List<List<String>> data, Font headerFont, Font cellFont) throws DocumentException {
        if (headers == null || headers.isEmpty()) {
            return;
        }
        
        PdfPTable table = new PdfPTable(headers.size());
        table.setWidthPercentage(100);
        table.setSpacingBefore(10);
        table.setSpacingAfter(10);
        
        // 添加表头
        for (String header : headers) {
            PdfPCell headerCell = new PdfPCell(new Phrase(header, headerFont));
            headerCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            headerCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            headerCell.setPadding(8);
            table.addCell(headerCell);
        }
        
        // 添加数据行
        if (data != null) {
            for (List<String> row : data) {
                for (String cell : row) {
                    PdfPCell dataCell = new PdfPCell(new Phrase(cell != null ? cell : "", cellFont));
                    dataCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    dataCell.setPadding(5);
                    table.addCell(dataCell);
                }
            }
        }
        
        document.add(table);
    }
    
    /**
     * PDF内容配置类
     */
    public static class PdfContent {
        private String title;
        private List<PdfSection> sections;
        
        public PdfContent() {}
        
        public PdfContent(String title, List<PdfSection> sections) {
            this.title = title;
            this.sections = sections;
        }
        
        // Getters and Setters
        public String getTitle() {
            return title;
        }
        
        public void setTitle(String title) {
            this.title = title;
        }
        
        public List<PdfSection> getSections() {
            return sections;
        }
        
        public void setSections(List<PdfSection> sections) {
            this.sections = sections;
        }
    }
    
    /**
     * PDF章节配置类
     */
    public static class PdfSection {
        private String sectionTitle;
        private String content;
        private List<String> tableHeaders;
        private List<List<String>> tableData;
        
        public PdfSection() {}
        
        public PdfSection(String sectionTitle, String content) {
            this.sectionTitle = sectionTitle;
            this.content = content;
        }
        
        public PdfSection(String sectionTitle, List<String> tableHeaders, List<List<String>> tableData) {
            this.sectionTitle = sectionTitle;
            this.tableHeaders = tableHeaders;
            this.tableData = tableData;
        }
        
        // Getters and Setters
        public String getSectionTitle() {
            return sectionTitle;
        }
        
        public void setSectionTitle(String sectionTitle) {
            this.sectionTitle = sectionTitle;
        }
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
        
        public List<String> getTableHeaders() {
            return tableHeaders;
        }
        
        public void setTableHeaders(List<String> tableHeaders) {
            this.tableHeaders = tableHeaders;
        }
        
        public List<List<String>> getTableData() {
            return tableData;
        }
        
        public void setTableData(List<List<String>> tableData) {
            this.tableData = tableData;
        }
    }
}