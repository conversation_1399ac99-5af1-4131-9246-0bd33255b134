package com.kbao.kbcelms.enterprise.base.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBeneficiary;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 最终受益人信息DAO
 * <AUTHOR>
 * @date 2025-07-31
 */
@Repository
public class EnterpriseBeneficiaryDao extends BaseMongoDaoImpl<EnterpriseBeneficiary, String> {

    /**
     * 根据统一社会信用代码查询最终受益人信息
     * @param creditCode 统一社会信用代码
     * @return 最终受益人信息列表
     */
    public List<EnterpriseBeneficiary> findByCreditCode(String creditCode) {
        Criteria criteria = Criteria.where("creditCode").is(creditCode);
        Query query = new Query().addCriteria(criteria);
        return this.find(query);
    }
    
    /**
     * 根据统一社会信用代码删除最终受益人信息
     * @param creditCode 统一社会信用代码
     * @param tenantId 租户ID
     */
    public void deleteByCreditCode(String creditCode, String tenantId) {
        Criteria criteria = Criteria.where("creditCode").is(creditCode)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        this.remove(query);
    }
}
