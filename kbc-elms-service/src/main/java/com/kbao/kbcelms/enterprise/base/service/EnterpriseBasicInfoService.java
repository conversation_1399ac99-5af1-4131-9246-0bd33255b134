package com.kbao.kbcelms.enterprise.base.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.base.dao.EnterpriseBasicInfoDao;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 企业基本信息业务逻辑层
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class EnterpriseBasicInfoService extends BaseMongoServiceImpl<EnterpriseBasicInfo, String, EnterpriseBasicInfoDao> {

    /**
     * 分页查询企业基本信息列表
     * @param page 分页查询参数
     * @return 分页结果
     */
    public PageInfo<EnterpriseBasicInfo> page(PageRequest<EnterpriseBasicInfo> page) {
        EnterpriseBasicInfo queryParam = page.getParam();
        String tenantId = ElmsContext.getTenantId();

        Criteria criteria = new Criteria();
        // 添加租户条件
        criteria.and("tenantId").is(tenantId);

        // 添加查询条件
        if (queryParam != null) {
            if (StringUtils.hasText(queryParam.getName())) {
                criteria.and("name").regex(queryParam.getName(), "i"); // 忽略大小写的模糊查询
            }
            if (StringUtils.hasText(queryParam.getCreditCode())) {
                criteria.and("creditCode").is(queryParam.getCreditCode());
            }
            if (StringUtils.hasText(queryParam.getRegStatus())) {
                criteria.and("regStatus").is(queryParam.getRegStatus());
            }
            if (StringUtils.hasText(queryParam.getIndustry())) {
                criteria.and("industry").regex(queryParam.getIndustry(), "i"); // 忽略大小写的模糊查询
            }
        }

        Pagination<EnterpriseBasicInfo> pagination = new Pagination<>(page.getPageNum(), page.getPageSize(), "updateTime desc");
        return super.page(new Query(criteria), pagination);
    }

    /**
     * 根据统一社会信用代码查询企业基本信息
     * @param creditCode 统一社会信用代码
     * @return 企业基本信息
     */
    public EnterpriseBasicInfo findByCreditCode(String creditCode) {
        return dao.findByCreditCode(creditCode);
    }
    
    /**
     * 根据企业名称查询企业基本信息
     * @return 企业基本信息
     */
    public List<EnterpriseBasicInfo> searchByName(String name) {
        return dao.searchByName(name);
    }

    public EnterpriseBasicInfo queryByFullName(String name) {
        return dao.queryByFullName(name);
    }
    
    /**
     * 保存或更新企业基本信息
     * @param basicInfo 企业基本信息
     * @return 保存后的企业基本信息
     */
    public EnterpriseBasicInfo saveOrUpdate(EnterpriseBasicInfo basicInfo) {
        Date now = new Date();

        // 设置租户ID
        if (basicInfo.getTenantId() == null) {
            basicInfo.setTenantId(ElmsContext.getTenantId());
        }
        // 从staffNumRange中提取最后一个数字并设置到staffNum
        Integer staffNum = extractStaffNumFromRange(basicInfo.getStaffNumRange());
        basicInfo.setStaffNum(staffNum);
        // 获取最小行业代码
        String minCategoryCode = this.getMinCategoryCode(basicInfo.getIndustryAll());
        basicInfo.setMinCategoryCode(minCategoryCode);
        // 检查是否已存在
        EnterpriseBasicInfo existing = findByCreditCode(basicInfo.getCreditCode());
        if (existing != null) {
            // 更新现有记录
            basicInfo.setId(existing.getId());
            basicInfo.setCreateTime(existing.getCreateTime());
            basicInfo.setUpdateTime(now); // 设置更新时间
        } else {
            // 新建记录
            basicInfo.setCreateTime(now);
            basicInfo.setUpdateTime(now);
        }

        return dao.saveOrUpdate(basicInfo);
    }

    /**
     * 从staffNumRange中提取最后一个数字并设置到staffNum字段
     * 例如：≥1000人<5000人 -> 5000
     */
    private Integer extractStaffNumFromRange(String staffNumRange) {
        if (!StringUtils.hasText(staffNumRange)) {
            return null;
        }

        // 使用正则表达式找到最后一个数字
        Matcher matcher = Pattern.compile("(\\d+)(?!.*\\d)").matcher(staffNumRange);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return null;
    }

    public String getMinCategoryCode(EnterpriseBasicInfo.IndustryAll industryAll) {
        if (industryAll == null) {
            return null;
        }
        return industryAll.getCategoryCodeFourth() != null ? industryAll.getCategoryCodeFourth() :
               industryAll.getCategoryCodeThird() != null ? industryAll.getCategoryCodeThird() :
               industryAll.getCategoryCodeSecond() != null ? industryAll.getCategoryCodeSecond() :
               industryAll.getCategoryCodeFirst();
    }

}
