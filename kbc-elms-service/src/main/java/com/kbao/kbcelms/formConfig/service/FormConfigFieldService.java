package com.kbao.kbcelms.formConfig.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.export.ExcelUtils;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.formConfig.model.FormConfigField;
import com.kbao.kbcelms.formConfig.dao.FormConfigFieldDao;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 表单配置字段服务类
 * @Date 2025-08-15
 */
@Service
public class FormConfigFieldService extends BaseMongoServiceImpl<FormConfigField, String, FormConfigFieldDao> {
    
    /**
     * 批量保存字段
     * @param configId 配置ID
     * @param fields 字段列表
     */
    public void saveBatch(Integer configId, List<FormConfigField> fields) {
        // 先删除原有字段
        dao.deleteByConfigId(configId);

        // 保存新字段
        if (fields != null && !fields.isEmpty()) {
            for (FormConfigField field : fields) {
                field.setConfigId(configId);
                // 如果没有设置排序，默认为1
                if (field.getSort() == null) {
                    field.setSort(1);
                }
                field.setCreateTime(new Date());
                dao.save(field);
            }
        }
    }
    
    /**
     * 根据配置ID获取字段列表
     * @param configId 配置ID
     * @return 字段列表
     */
    public List<FormConfigField> getFieldList(Integer configId) {
        return dao.findByConfigIdOrderBySort(configId);
    }

    public List<FormConfigField> findVisibleFields(Integer configId) {
        return dao.findVisibleFields(configId);
    }

    public List<FormConfigField> getByConfigIds(List<Integer> configIds) {
        return dao.getByConfigIds(configIds);
    }
    
    /**
     * 根据配置ID统计字段数量
     * @param configId 配置ID
     * @return 字段数量
     */
    public long countByConfigId(Integer configId) {
        return dao.countByConfigId(configId);
    }

    /**
     * 获取导出字段列表
     * @param configId 配置ID
     * @return 导出字段列表
     */
    public List<FormConfigField> getExportFieldList(Integer configId) {
        List<FormConfigField> fieldList = this.getFieldList(configId);
        for (FormConfigField field : fieldList) {
            if (field.getAdditional() != null) {
                field.setAdditionalStr(field.getAdditional().toJSONString());
            }
        }
        return fieldList;
    }

    /**
     * 导入字段列表（仅解析，不保存）
     * @param configId 配置ID
     * @param file Excel文件
     * @return 解析后的字段列表
     */
    public List<FormConfigField> importFields(Integer configId, MultipartFile file) {
        ExcelUtils<FormConfigField> excelUtils = new ExcelUtils<>(FormConfigField.class);
        List<FormConfigField> formConfigFields = excelUtils.readExcel(FormConfigField.class, file);
        for (FormConfigField field : formConfigFields) {
            if (EmptyUtils.isNotEmpty(field.getAdditionalStr())) {
                field.setAdditional(JSONObject.parseObject(field.getAdditionalStr()));
            }
        }
        return formConfigFields;
    }
}
