package com.kbao.kbcelms.opportunityteam.dao;


import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam;
import com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 机会成员接口
 */
public interface OpportunityTeamMapper  extends BaseMapper<OpportunityTeam, Integer>{

    /**
     * 查询机会项目成员
     *
     * @param opportunityId
     * @param tenantId
     * @return
     */
    List<OpportunityTeamMember> selectMember(@Param("opportunityId") Integer opportunityId,@Param("tenantId") String tenantId);

    /**
     * 获取单个项目成员
     *
     * @param id
     * @param roleType
     * @param opportunityId
     * @param tenantId
     * @return
     */
    OpportunityTeamMember findOneMember(@Param("id") Integer id,@Param("roleType") Integer roleType,@Param("opportunityId") Integer opportunityId,@Param("tenantId") String tenantId);


    /**
     * 查询项目经理
     *
     * @param opportunityId
     * @return
     */
    OpportunityTeam selectManager(@Param("opportunityId") Integer opportunityId);

    /**
     * 批量查询机会团队信息 by 机会IDs
     *
     * @param opportunityIds 机会ID列表
     * @return 机会团队列表
     */
    List<OpportunityTeam> selectManagers(@Param("opportunityIds") List<Integer> opportunityIds);

    /**
     * 修改成员角色
     *
     * @param opportunityTeam
     */
    void updateMember(OpportunityTeam opportunityTeam);


    /**
     * 根据机会id删除项目成员
     *
     * @param opportunityId
     */
    void deleteByOpportunityId(@Param("opportunityId") Integer opportunityId);

    /**
     * 查询待参与的机会ID列表
     * 查询在项目团队中属于专家角色且join_type为待确认（0）的机会ID列表
     *
     * @param param 查询参数
     * @return 机会ID列表
     */
    List<Integer> selectPendingParticipationOpportunityIds(Map<String, Object> param);

    /**
     * 分页查询待参与的机会ID列表
     * 查询在项目团队中属于专家角色且join_type为待确认（0）的机会ID列表（支持分页）
     *
     * @param param 查询参数
     * @return 机会ID列表
     */
    List<Integer> selectPendingParticipationOpportunityIdsPage(Map<String, Object> param);
}