package com.kbao.kbcelms.opportunityfiles.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.enums.FileTypeEnum;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;
import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;
import com.kbao.kbcelms.opportunityfiles.dao.OpportunityFilesDao;
import com.kbao.kbcelms.opportunityfiles.model.OpportunityFiles;
import com.kbao.kbcelms.ufs.FileWebService;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.IDUtils;
import com.kbao.tool.util.StringUtil;
import com.kbao.tool.util.SysLoginUtils;

import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 机会文件资料表
 */
@Service
public class OpportunityFilesService extends BaseMongoServiceImpl<OpportunityFiles, String, OpportunityFilesDao> {

    @Autowired
    FileWebService fileWebService;

    @Autowired
    OpportunityDetailService opportunityDetailService;

    /**
     * 根据机会id获取文件资料
     *
     * @param opportunityId
     * @return
     */
    public List<OpportunityFiles> findByOpportunityId(Integer opportunityId) {
        if (opportunityId == null) {
            throw new BusinessException("机会id不能为空");
        }

        return this.dao.findByOpportunityId(opportunityId);
    }

    /**
     * 删除机会文件资料
     *
     * @param fileId
     */
    public void remove(String fileId) {
        if (StringUtil.isEmpty(fileId)) {
            throw new BusinessException("资料id不能为空");
        }

        this.dao.remove(fileId);
    }


    /**
     * 上传文件
     *
     * @param appCode
     * @param file
     * @param opportunityId
     * @param fileType
     * @param companyId
     * @param companyName
     */
    public void upload(String appCode, MultipartFile file, Integer opportunityId,String fileType,String companyId,String companyName,String createId,String createName) {
        // 上传文件
        String businessNo = IDUtils.randomUUID();
        FileUploadResponse response = fileWebService.upload(appCode,businessNo,file,null,file.getOriginalFilename(), FileTypeEnum.getByKey(fileType));
        if(response == null) {
            throw new BusinessException("上传文件结果为空");
        }
        OpportunityFiles newFile = new OpportunityFiles();
        newFile.setOpportunityId(opportunityId);
        newFile.setFileType(fileType);
        newFile.setFileName(file.getOriginalFilename());
        newFile.setFilePath(response.getForeignPath());
        newFile.setCompanyId(companyId);
        newFile.setCompanyName(companyName);
        newFile.setCreateTime(new Date());
        newFile.setCreateId(createId);
        newFile.setCreateName(createName);
        this.dao.save(newFile);

        if (FileTypeEnum.RESULT.equals(FileTypeEnum.getByKey(fileType))) {
            // 更新机会明细表的排分时间
            OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(opportunityId, SysLoginUtils.getUser().getTenantId());
            if (detail != null) {
                //时间格式化为当前时间 2025-08-15 10:00:00
                String rankingTime = DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
                detail.setRankingTime(rankingTime);
                detail.setUpdateId(SysLoginUtils.getUserId());
                detail.setUpdateTime(new Date());
                opportunityDetailService.updateByPrimaryKeySelective(detail);
            }
        }
    }
}