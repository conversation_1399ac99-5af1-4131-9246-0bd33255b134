package com.kbao.kbcelms.genAgentEnterprise.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.bascode.entity.BasCode;import com.kbao.kbcelms.bascode.service.BasCodeService;import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;import com.kbao.kbcelms.enterprise.type.bean.EnterpriseScaleEnumVO;import com.kbao.kbcelms.enterprise.type.model.EnterpriseType;import com.kbao.kbcelms.enterprise.type.service.EnterpriseTypeService;import com.kbao.kbcelms.enterprise.util.EnterpriseTypeRuleUtil;
import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListReqVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListResVo;import com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.industry.entity.Industry;import com.kbao.kbcelms.industry.service.IndustryService;import com.kbao.kbcelms.util.StringUtils;import com.kbao.tool.util.EmptyUtils;import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 顾问企业表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Service
public class GenAgentEnterpriseService extends BaseSQLServiceImpl<GenAgentEnterprise, Integer, GenAgentEnterpriseMapper> {

    @Autowired
    private EnterpriseTypeService enterpriseTypeService;
    @Autowired
    private BasCodeService basCodeService;
    @Autowired
    private IndustryService industryService;

    /**
     * 分页查询顾问企业列表
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    public PageInfo<AgentEnterpriseListResVo> getAgentEnterpriseList(PageRequest<AgentEnterpriseListReqVo> pageRequest) {
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<AgentEnterpriseListResVo> list = mapper.getAgentEnterpriseList(pageRequest.getParam());
        if (EmptyUtils.isNotEmpty(list)) {
            EnterpriseScaleEnumVO scaleEnum = enterpriseTypeService.getEnterpriseScaleEnum();
        }
        return new PageInfo<>(list);
    }

    public GenAgentEnterprise toGenAgentEnterprise(Integer agentEnterpriseId, EnterpriseBasicInfo info) {
        GenAgentEnterprise enterprise = new GenAgentEnterprise();
        enterprise.setName(info.getName());
        enterprise.setCreditCode(info.getCreditCode());
        enterprise.setEnterpriseScale(info.getScale());
        if (info.getLatestBusinessIncome() != null || info.getStaffNum() != null) {
            List<EnterpriseType> enterpriseTypes = enterpriseTypeService.getEnterpriseTypes();
            enterpriseTypes.forEach(enterpriseType -> {
                enterpriseType.getRules().forEach(rule -> {
                    if (info.getLatestBusinessIncome() != null
                        && "revenue".equals(rule.getField())
                        && (rule.getMinValue() == null || info.getLatestBusinessIncome() >= rule.getMinValue())
                        && (rule.getMaxValue() == null || info.getLatestBusinessIncome() < rule.getMaxValue())) {
                        enterprise.setAnnualIncome(enterpriseType.getCode());
                    }
                    if (info.getStaffNum() != null
                        && "employeeCount".equals(rule.getField())
                        && (rule.getMinValue() == null || info.getStaffNum() >= rule.getMinValue())
                        && (rule.getMaxValue() == null || info.getStaffNum() < rule.getMaxValue())) {
                        enterprise.setStaffScale(enterpriseType.getCode());
                    }
                });
            });
        }
        String dtType = StringUtils.getDtType(enterprise.getStaffScale(), enterprise.getAnnualIncome());
        enterprise.setDtType(dtType);
        if (info.getDistrictCode() != null) {
            enterprise.setDistrictCode(info.getDistrictCode());
            BasCode byCode = basCodeService.getByCode(info.getDistrictCode());
            if (byCode != null) {
                enterprise.setCity(byCode.getName());
            }
        }
        if (info.getMinCategoryCode() != null) {
            enterprise.setCategoryCode(info.getMinCategoryCode());
            Industry industry = industryService.selectByCode(info.getMinCategoryCode());
            if (industry != null) {
                enterprise.setCategoryName(industry.getName());
            }
        }
        enterprise.setIsVerified("1");
        if (agentEnterpriseId != null) {
            enterprise.setId(agentEnterpriseId);
            GenAgentEnterprise oldEnterprise = mapper.selectByPrimaryKey(agentEnterpriseId);
            if (oldEnterprise != null) {
                enterprise.setEnterpriseContacter(oldEnterprise.getEnterpriseContacter());
                enterprise.setContacterPhone(oldEnterprise.getContacterPhone());
                enterprise.setRemark(oldEnterprise.getRemark());
            }
        }
        return enterprise;
    }

    /**
     * 根据统一信用代码查询企业信息
     */
    public List<GenAgentEnterprise> getByCreditCode(String creditCode) {
        return mapper.getByCreditCode(creditCode);
    }


}
