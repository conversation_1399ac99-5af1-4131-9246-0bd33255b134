package com.kbao.kbcelms.ufs;

import com.alibaba.fastjson.JSON;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.SystemClock;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.appfilechannel.bean.AppFileChannelListVo;
import com.kbao.kbcbsc.tenant.entity.Tenant;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.bsc.BscClientService;
import com.kbao.kbcelms.bsc.KbcBscService;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcucs.context.RequestContext;
import com.kbao.kbcufs.adapter.BaseClientAdapter;
import com.kbao.kbcufs.adapter.FileClientAdapter;
import com.kbao.kbcufs.enums.FileTypeEnum;
import com.kbao.kbcufs.file.vo.client.FileUploadRequest;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.kbcufs.utils.SignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;


@Service
@Slf4j
public class KbcUfsService extends BaseClientAdapter {

    @Autowired
    private FileClientAdapter fileClientAdapter;

    @Autowired
    private KbcBscService kbcBscService;

    @Autowired
    private BscClientService bscClientService;

    /**
     * 上传图片
     *
     * @param file
     * @return 文件的绝对地址
     */
    public FileUploadResponse upload(String appCode, String businessNo, MultipartFile file, String content, String path, FileTypeEnum fileType) {
        log.info("FileServiceAdapter:upload >>" + appCode);
        if (StringUtils.isBlank(content) && file == null) {
            throw new BusinessException("文件内容不可为空");
        }
        String tenantId = ElmsContext.getTenantId();
        String userId = ElmsContext.getUser().getUserId();

        log.info("getFileChannelConfig >> tenantId:" + tenantId + "--appCode--" + appCode + "--userId--" + userId);
        AppFileChannelListVo config = bscClientService.getFileChannelConfig(tenantId, appCode, "1");
        Tenant tenant = bscClientService.getTenant(tenantId);
        FileUploadRequest request = new FileUploadRequest();
        request.setUserName(config.getAccount());
        request.setBusinessNo(businessNo);
        request.setBusinessTenantId(tenantId);
        request.setBusinessTenantName(tenant.getTenantName());
        if (StringUtils.isNotBlank(content)) {
            if (content.startsWith("data:image/") || content.startsWith("data:Image/")) {
                content = content.substring(content.indexOf(",") + 1);
                request.setType(FileTypeEnum.BASE64.getType());
            } else if (content.startsWith("http://") || content.startsWith("https://")) {
                request.setType(FileTypeEnum.NETWORK.getType());
            } else {
                request.setType(FileTypeEnum.TEXT.getType());
            }
            request.setContent(content);
        } else {
            request.setType(FileTypeEnum.FILE.getType());
            request.setFile(file);
        }
        request.setFileType(fileType.getType());
        request.setPath(path);
        request.setCreateUser(userId);
        request.setTimestamp(SystemClock.nowDate());
        // 账号类型。取值范围：内网账号:intranet；外网账号：internet
        request.setNetwork("sts");
        request.setCover(true);
        request.setSign(SignatureUtil.getSign(request, config.getSecretKey()));
        return this.uploadFile(request);
    }

    public FileUploadResponse uploadFile(FileUploadRequest request) {
        setBaseFeignHeader(request.getBusinessTenantId());
        Result<FileUploadResponse> result = fileClientAdapter.upload(request);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(result.getResp_code())) {
            return result.getDatas();
        } else {
            KbcUfsService.log.error("文件上传失败，Cause:{}", JSON.toJSONString(result));
            throw new BusinessException("文件上传失败");
        }
    }
    /**
     * 设置feign请求头
     *
     * @param tenantId 租户id
     */
    private void setBaseFeignHeader(String tenantId) {
        Map<String, String> headerMap = new HashMap<>(2);
        headerMap.put("tenantId", tenantId);
        FeignRequestHeader.Header.set(headerMap);
    }

}
