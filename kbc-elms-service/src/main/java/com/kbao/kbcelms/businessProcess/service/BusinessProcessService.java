package com.kbao.kbcelms.businessProcess.service;

import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.businessProcess.bean.BusinessProcessRequest;
import com.kbao.kbcelms.businessProcess.bean.BusinessProcessVO;
import com.kbao.kbcelms.businessProcess.dao.BusinessProcessDao;
import com.kbao.kbcelms.businessProcess.model.BusinessProcess;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务流程配置业务逻辑层
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class BusinessProcessService extends BaseMongoServiceImpl<BusinessProcess, String, BusinessProcessDao> {

    /**
     * 获取所有业务流程配置
     * @return 业务流程配置列表
     */
    public List<BusinessProcessVO> getAllBusinessProcess() {
        String tenantId = ElmsContext.getTenantId();
        
        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(tenantId);
        
        Query query = new Query(criteria);
        List<BusinessProcess> businessProcessList = this.dao.find(query);
        
        if (CollectionUtils.isEmpty(businessProcessList)) {
            return new ArrayList<>();
        }
        
        return businessProcessList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 批量保存业务流程配置
     * @param requestList 请求参数列表
     */
    public void saveBatchBusinessProcess(List<BusinessProcessRequest> requestList) {
        if (CollectionUtils.isEmpty(requestList)) {
            return;
        }
        
        String tenantId = SysLoginUtils.getUser().getTenantId();
        String currentUser = SysLoginUtils.getUser().getUserId();
        Date currentTime = new Date();
        
        // 先删除当前租户的所有配置
        Criteria deleteCriteria = new Criteria();
        deleteCriteria.and("tenantId").is(tenantId);
        Query deleteQuery = new Query(deleteCriteria);
        this.dao.remove(deleteQuery);
        for (BusinessProcessRequest request : requestList) {
            BusinessProcess businessProcess = new BusinessProcess();
            BeanUtils.copyProperties(request, businessProcess);
            
            businessProcess.setTenantId(tenantId);
            businessProcess.setCreateTime(currentTime);
            businessProcess.setCreateId(currentUser);
            this.dao.save(businessProcess);
        }
    }

    /**
     * 根据业务编码获取流程配置
     * @param businessCode 业务编码
     * @return 流程配置列表
     */
    public List<String> getProcessConfigsByBusinessCode(String businessCode) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        
        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(tenantId);
        criteria.and("businessCode").is(businessCode);
        
        Query query = new Query(criteria);
        BusinessProcess businessProcess = this.dao.findOne(query);
        
        if (businessProcess == null || CollectionUtils.isEmpty(businessProcess.getProcessConfigs())) {
            return new ArrayList<>();
        }
        
        return businessProcess.getProcessConfigs();
    }

    /**
     * 检查指定业务是否启用了某个流程
     * @param businessCode 业务编码
     * @param processCode 流程编码
     * @return 是否启用
     */
    public boolean isProcessEnabled(String businessCode, String processCode) {
        List<String> processConfigs = getProcessConfigsByBusinessCode(businessCode);
        return processConfigs.contains(processCode);
    }

    /**
     * 转换为VO对象
     * @param businessProcess 实体对象
     * @return VO对象
     */
    private BusinessProcessVO convertToVO(BusinessProcess businessProcess) {
        BusinessProcessVO vo = new BusinessProcessVO();
        BeanUtils.copyProperties(businessProcess, vo);
        return vo;
    }
}
