package com.kbao.kbcelms.opportunityorder.dao;


import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder;
/**
 * 机会订单数据访问层
 * @luobb
 */
public interface OpportunityOrderMapper extends BaseMapper<OpportunityOrder, Integer> {
    /**
     * 判断订单是否唯一
     * @param order 包含查询参数的OpportunityOrder对象
     * @return 符合条件的记录数
     */
    int selectCountByUniqueParams(OpportunityOrder order);
}