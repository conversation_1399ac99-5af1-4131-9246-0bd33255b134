package com.kbao.kbcelms.opportunityorder.service;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder;
import com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper;
import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;
import lombok.extern.slf4j.Slf4j;

/**
 * 机会订单服务
 * @luobb
 */
@Slf4j
@Service
public class OpportunityOrderService extends BaseSQLServiceImpl<OpportunityOrder, Integer, OpportunityOrderMapper> {

    @Autowired
    private OpportunityDetailService opportunityDetailService;

    /**
     * 新增机会保单
     * @param opportunityOrder
     * @return
     */
    public OpportunityOrder addOpportunityOrder(OpportunityOrder opportunityOrder) {
        // 检查保单是否唯一
        int count = this.mapper.selectCountByUniqueParams(opportunityOrder);
        if (count > 0) {
            throw new BusinessException("保单[" + opportunityOrder.getPolicyNo() + "]已存在");
        }
        initOpportunityOrder(opportunityOrder);
        insert(opportunityOrder);
        
        // 检查是否是第一条保单，如果是则更新机会详情表的出单时间
        checkAndUpdateFirstPolicyTime(opportunityOrder.getOpportunityId(), opportunityOrder.getTenantId());
        
        return opportunityOrder;
    }

    private void initOpportunityOrder(OpportunityOrder opportunityOrder){
        String tenantId = SysLoginUtils.getUser().getTenantId();
        opportunityOrder.setCreateId(BscUserUtils.getUserId());
        opportunityOrder.setCreateTime(DateUtils.getCurrentDate());
        opportunityOrder.setUpdateId(BscUserUtils.getUserId());
        opportunityOrder.setUpdateTime(DateUtils.getCurrentDate());
        opportunityOrder.setTenantId(tenantId);
    }

    /**
     * 检查并更新第一条保单的出单时间
     * @param opportunityId 机会ID
     * @param tenantId 租户ID
     */
    private void checkAndUpdateFirstPolicyTime(String opportunityId, String tenantId) {
        try {
            // 先查询机会详情，检查出单时间是否已有值
            OpportunityDetail detail = opportunityDetailService.selectByOpportunityId(Integer.valueOf(opportunityId), tenantId);
            if (detail != null && EmptyUtils.isNotEmpty(detail.getPolicyTime())) {
                // 如果出单时间字段已有值，则不再修改
                log.info("机会ID：{} 的出单时间已有值：{}，跳过更新", opportunityId, detail.getPolicyTime());
                return;
            }
            // 更新机会详情表的出单时间
            String currentTime = DateUtils.dateTime2Str(DateUtils.getCurrentDate(), "yyyy-MM-dd HH:mm:ss");
            opportunityDetailService.updatePolicyTimeByOpportunityId(opportunityId, currentTime);
            log.info("机会ID：{} 更新出单时间为：{}", opportunityId, currentTime);
        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("更新第一条保单出单时间失败，机会ID：{}，错误：{}", opportunityId, e.getMessage(), e);
        }
    }

    /**
     * 修改机会保单
     * @param opportunityOrder
     * @return
     */
    public OpportunityOrder updateOpportunityOrder(OpportunityOrder opportunityOrder) {
        OpportunityOrder existingOrder = this.selectByPrimaryKey(opportunityOrder.getId());

        if (existingOrder == null) {
           throw new BusinessException("机会保单不存在");
        }

        if(existingOrder.getIsDeleted() == 1) {
            throw new BusinessException("机会保单已删除");
        }

        // 检查保单是否唯一
        int count = this.mapper.selectCountByUniqueParams(opportunityOrder);
        if (count > 0) {
            throw new BusinessException("保单[" + opportunityOrder.getPolicyNo() + "]已存在");
        }

        existingOrder.setOrderCode(opportunityOrder.getOrderCode());
        existingOrder.setPolicyNo(opportunityOrder.getPolicyNo());
        existingOrder.setCompanyCode(opportunityOrder.getCompanyCode());
        existingOrder.setCompanyName(opportunityOrder.getCompanyName());
        existingOrder.setUpdateId(BscUserUtils.getUserId());
        existingOrder.setUpdateTime(DateUtils.getCurrentDate());
        updateByPrimaryKey(existingOrder);
        return existingOrder;
    }

    /**
     * 删除机会保单
     * @param id
     */
    public void deleteOpportunityOrder(Integer id) {
        OpportunityOrder existingOrder = this.selectByPrimaryKey(id);
        if (existingOrder == null) {
            throw new BusinessException("机会保单不存在");
        }
        existingOrder.setIsDeleted(1);
        existingOrder.setUpdateId(BscUserUtils.getUserId());
        existingOrder.setUpdateTime(DateUtils.getCurrentDate());
        updateByPrimaryKey(existingOrder);
    }

    /**
     * 根据机会id查询机会保单列表
     * @param opportunityId
     * @return
     */
    public List<OpportunityOrder> queryOpportunityOrderListByOpportunityId(String opportunityId) {
        Map<String, Object> param = new HashMap<>();
        param.put("opportunityId", opportunityId);
        param.put("isDeleted", 0);
        return selectByParam(param);
    }


    /**
     * 批量新增机会保单
     * @param opportunityOrders
     * @return
     */
    public String batchAddOpportunityOrder(List<OpportunityOrder> opportunityOrders) {

        if(EmptyUtils.isEmpty(opportunityOrders)){
            return null;
        }

        StringBuffer sb = new StringBuffer();
        for (OpportunityOrder opportunityOrder : opportunityOrders) {
            // 检查保单是否唯一
            int count = this.mapper.selectCountByUniqueParams(opportunityOrder);
            if (count > 0) {
                sb.append("保单[" + opportunityOrder.getPolicyNo() + "]已存在;");
            }
            else {
                initOpportunityOrder(opportunityOrder);
                insert(opportunityOrder);
            }
        }

        // 批量新增后，检查每个机会是否需要更新出单时间
        if (sb.length() == 0) { // 只有当所有保单都新增成功时才检查
            for (OpportunityOrder opportunityOrder : opportunityOrders) {
                checkAndUpdateFirstPolicyTime(opportunityOrder.getOpportunityId(), opportunityOrder.getTenantId());
            }
        }

        return sb.toString();
    }
}
