package com.kbao.kbcelms.usertenant.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.usertenant.entity.UserTenant;
import org.apache.ibatis.annotations.Param;

public interface UserTenantMapper extends BaseMapper<UserTenant, Integer> {

    /**
     * 根据用户ID、租户ID获取关联数据
     *
     * @param userId
     * @param tenantId
     * <AUTHOR>
     * @Date 2025/8/14 16:02
     */
    UserTenant getByUserIdAndTenantId(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 修改关联关系状态
     *
     * @param userTenant
     * <AUTHOR>
     * @Date 2025/8/14 16:01
     */
    void setUserStatus(UserTenant userTenant);

    /**
     * 根据用户ID、租户ID删除用户数据
     *
     * @param userTenant
     * <AUTHOR>
     * @Date 2025/8/14 16:01
     */
    void deleteByUserIdAndTenantId(UserTenant userTenant);

    /**
     * 云服同步删除elms用户 - 根据userId删除用户
     *
     * @param userTenant
     * <AUTHOR>
     * @Date 2025/8/14 16:01
     */
    void deleteByUserId(UserTenant userTenant);
}