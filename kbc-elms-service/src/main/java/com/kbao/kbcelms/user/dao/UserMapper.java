package com.kbao.kbcelms.user.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.user.entity.User;
import com.kbao.kbcelms.user.vo.UserResponseVO;
import com.kbao.kbcelms.user.vo.UserRoleAuthVO;
import com.kbao.kbcelms.usertenant.entity.UserTenant;import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface UserMapper extends BaseMapper<User, Integer> {

    /**
     * 根据云服账号、租户ID获取用户信息
     *
     * @param tenantId
     * @param bscUseName
     * <AUTHOR>
     * @Date 2025/8/14 16:02
     */
    User getByBscUserNameAndTenantId(@Param("tenantId") String tenantId, @Param("bscUseName") String bscUseName);

    /**
     * 根据userId获取用户数据
     *
     * @param userId
     * <AUTHOR>
     * @Date 2025/8/14 16:03
     */
    User findByUserId(@Param("userId") String userId);

    /**
     * 根据云服账号获取用户数据
     *
     * @param bscUseName
     * <AUTHOR>
     * @Date 2025/8/14 16:03
     */
    User findByBscUserName(@Param("bscUseName") String bscUseName);

    /**
     * 根据UserId、租户ID获取用户角色、权限等信息
     *
     * @param userId
     * @param tenantId
     * <AUTHOR>
     * @Date 2025/8/14 16:03
     */
    UserRoleAuthVO getUserRoleAuthByUserId(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 分页查询
     *
     * @param params
     * <AUTHOR>
     * @Date 2025/8/14 16:04
     */
    List<UserResponseVO> findByCondition(Map<String, Object> params);

    /**
     * 用户详情
     *
     * @param params
     * <AUTHOR>
     * @Date 2025/8/14 16:04
     */
    UserResponseVO detail(Map<String, Object> params);

    /**
     * 云服同步删除elms用户
     *
     * @param user
     * <AUTHOR>
     * @Date 2025/8/14 16:04
     */
    void deleteByUserId(User user);

    /**
     * 查询分公司统筹角色人员清单
     * @param roleType 角色性质（必填）
     * @param organCode 机构编码（可为空，为空时查询所有机构）
     * @param nickName 用户姓名（可为空，为空时查询所有用户，不为空时进行模糊搜索）
     * @return 人员清单
     * <AUTHOR>
     * @date 2025/1/15 16:30
     */
    List<Map<String, Object>> selectBranchCoordinatorUsers(@Param("roleType") int roleType, @Param("organCode") String organCode, @Param("nickName") String nickName,@Param("tenantId") String tenantId);

    List<User> getByUserIds(List<String> userIds);
}