package com.kbao.kbcelms.onlineproduct.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.onlineproduct.entity.OnlineProductConfig;
import com.kbao.kbcelms.onlineproduct.vo.OnlineProductConfigVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线上产品配置 Mapper 接口
 * <AUTHOR>
 * @date 2025-01-15
 */
@Mapper
public interface OnlineProductConfigMapper extends BaseMapper<OnlineProductConfig, Long> {

    /**
     * 查询线上产品配置列表
     * @param param 查询参数
     * @return 配置列表
     */
    List<OnlineProductConfigVO> selectOnlineProductConfigList(Object param);

    /**
     * 根据ID查询线上产品配置详情
     * @param id 配置ID
     * @return 配置详情
     */
    OnlineProductConfigVO selectOnlineProductConfigById(@Param("id") Long id);

    /**
     * 检查配置是否存在
     * @param industryCode 行业代码
     * @param probability 风险概率
     * @param impact 风险影响
     * @param level 风险等级
     * @param id 排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    int checkConfigExists(@Param("industryCode") List<String> industryCode,
                         @Param("probability") String probability,
                         @Param("impact") String impact,
                         @Param("level") String level,
                         @Param("id") Long id);

    /**
     * 根据行业代码查询线上产品配置
     * @param industryCode 行业代码
     * @return 配置列表
     */
    List<OnlineProductConfig> selectByIndustryCode(@Param("industryCode") String industryCode);
}
