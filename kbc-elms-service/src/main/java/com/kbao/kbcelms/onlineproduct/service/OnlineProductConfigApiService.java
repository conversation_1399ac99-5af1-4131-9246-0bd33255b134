package com.kbao.kbcelms.onlineproduct.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.service.IndustryService;
import com.kbao.kbcelms.onlineproduct.dao.OnlineProductConfigMapper;
import com.kbao.kbcelms.onlineproduct.entity.OnlineProductConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

@Service
public class OnlineProductConfigApiService extends BaseSQLServiceImpl<OnlineProductConfig, Long, OnlineProductConfigMapper> {

    private final ObjectMapper objectMapper = new ObjectMapper();


    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;

    @Autowired
    private IndustryService industryService;


    /**
     * 根据企业客户ID获取可配置的险种类型
     * @param enterpriseId 企业客户ID
     * @return 险种类型编码数组
     */
    public List<String> getInsuranceTypesByEnterprise(Integer enterpriseId) {
        try {
            logger.info("开始根据企业客户ID获取险种类型，企业ID: {}", enterpriseId);

            // 1. 根据企业客户ID获取企业基本信息
            GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(enterpriseId);
            if (enterprise == null) {
                logger.warn("未找到企业信息，企业ID: {}", enterpriseId);
                return new ArrayList<>();
            }

            // 2. 获取企业的行业分类代码
            List<String> enterpriseIndustryCodes = getEnterpriseIndustryCodes(enterprise);
            if (enterpriseIndustryCodes.isEmpty()) {
                logger.warn("企业未配置行业分类，企业ID: {}, 企业名称: {}", enterpriseId, enterprise.getName());
                return new ArrayList<>();
            }

            logger.info("企业行业分类代码: {}, 企业ID: {}", enterpriseIndustryCodes, enterpriseId);

            // 3. 根据行业分类查询线上产品配置
            List<OnlineProductConfig> configs = queryConfigsByIndustryCodes(enterpriseIndustryCodes);
            if (configs.isEmpty()) {
                logger.warn("未找到匹配的线上产品配置，企业ID: {}, 行业代码: {}", enterpriseId, enterpriseIndustryCodes);
                return new ArrayList<>();
            }

            // 4. 汇总所有可配置的险种类型
            Set<String> allInsuranceTypes = new HashSet<>();
            for (OnlineProductConfig config : configs) {
                if (config.getEnabled() == 1 && config.getIsDeleted() == 0) {
                    List<String> configInsuranceTypes = parseInsuranceTypesFromConfig(config);
                    allInsuranceTypes.addAll(configInsuranceTypes);
                }
            }

            List<String> result = new ArrayList<>(allInsuranceTypes);
            logger.info("获取到险种类型: {}, 企业ID: {}", result, enterpriseId);

            return result;

        } catch (Exception e) {
            logger.error("根据企业客户ID获取险种类型失败，企业ID: {}", enterpriseId, e);
            throw new RuntimeException("获取险种类型失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取企业的行业分类代码
     * @param enterprise 企业基本信息
     * @return 行业分类代码列表
     */
    private List<String> getEnterpriseIndustryCodes(GenAgentEnterprise enterprise) {
        List<String> industryCodes = new ArrayList<>();

        // 从企业基本信息中提取行业分类代码
        if (enterprise.getCategoryCode() != null) {
            Industry industry = industryService.selectByCode(enterprise.getCategoryCode());
            String[] fullPathArray = industry.getFullPath().split("/");
            for (int i = 0; i < fullPathArray.length; i++) {
                if (i > 0) {
                    industryCodes.add(fullPathArray[i]);
                }
            }
        }

        return industryCodes;
    }

    /**
     * 根据行业代码查询线上产品配置
     * @param industryCodes 行业代码列表
     * @return 配置列表
     */
    private List<OnlineProductConfig> queryConfigsByIndustryCodes(List<String> industryCodes) {
        List<OnlineProductConfig> allConfigs = new ArrayList<>();

        for (String industryCode : industryCodes) {
            try {
                // 查询包含该行业代码的配置
                List<OnlineProductConfig> configs = mapper.selectByIndustryCode(industryCode);
                if (configs != null && !configs.isEmpty()) {
                    allConfigs.addAll(configs);
                }
            } catch (Exception e) {
                logger.warn("查询行业代码 {} 的配置失败", industryCode, e);
            }
        }

        return allConfigs;
    }

    /**
     * 从配置中解析险种类型
     * @param config 线上产品配置
     * @return 险种类型列表
     */
    private List<String> parseInsuranceTypesFromConfig(OnlineProductConfig config) {
        if (StringUtils.hasText(config.getInsuranceTypes())) {
            try {
                return objectMapper.readValue(config.getInsuranceTypes(),
                        new TypeReference<List<String>>() {});
            } catch (Exception e) {
                logger.warn("解析配置险种类型失败，配置ID: {}, 险种类型字符串: {}",
                        config.getId(), config.getInsuranceTypes(), e);
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }
}
