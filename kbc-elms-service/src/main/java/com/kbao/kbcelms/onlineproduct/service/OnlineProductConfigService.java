package com.kbao.kbcelms.onlineproduct.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.service.IndustryService;
import com.kbao.kbcelms.onlineproduct.bean.OnlineProductConfigQuery;
import com.kbao.kbcelms.onlineproduct.dao.OnlineProductConfigMapper;
import com.kbao.kbcelms.onlineproduct.entity.OnlineProductConfig;
import com.kbao.kbcelms.onlineproduct.vo.OnlineProductConfigVO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.tool.util.MapUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 线上产品配置服务实现类
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class OnlineProductConfigService extends BaseSQLServiceImpl<OnlineProductConfig, Long, OnlineProductConfigMapper> {

    private static final Logger logger = LoggerFactory.getLogger(OnlineProductConfigService.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
    private IndustryService industryService;

    /**
     * 分页查询线上产品配置
     * @param request 分页请求
     * @return 分页结果
     */
    public PageInfo<OnlineProductConfigVO> getPage(PageRequest<OnlineProductConfigQuery> request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<OnlineProductConfigVO> list = mapper.selectOnlineProductConfigList(MapUtils.objectToMap(request.getParam()));

        // 为每个VO设置状态名称、行业代码列表和险种类型列表
        for (OnlineProductConfigVO vo : list) {
            vo.setEnabledName(vo.getEnabled() ? "启用" : "禁用");
            
            // 解析行业代码JSON字符串
            List<String> industryCodes = parseIndustryCodes(vo);
            vo.setIndustryCode(industryCodes);
            
            // 解析险种类型JSON字符串
            List<String> insuranceTypes = parseInsuranceTypes(vo);
            vo.setInsuranceTypes(insuranceTypes);
        }

        return new PageInfo<>(list);
    }

    /**
     * 根据ID查询线上产品配置详情
     * @param id 配置ID
     * @return 配置详情
     */
    public OnlineProductConfigVO getById(Long id) {
        OnlineProductConfigVO vo = mapper.selectOnlineProductConfigById(id);
        if (vo != null) {
            vo.setEnabledName(vo.getEnabled() ? "启用" : "禁用");
            
            // 解析行业代码JSON字符串
            List<String> industryCodes = parseIndustryCodes(vo);
            vo.setIndustryCode(industryCodes);
            
            // 解析险种类型JSON字符串
            List<String> insuranceTypes = parseInsuranceTypes(vo);
            vo.setInsuranceTypes(insuranceTypes);
        }
        return vo;
    }

    /**
     * 新增线上产品配置
     * @param vo 配置信息
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public OnlineProductConfigVO add(OnlineProductConfigVO vo) {
        // 检查配置是否重复
        if (checkConfigExists(vo.getIndustryCode(), vo.getProbability(), vo.getImpact(), vo.getLevel(), null)) {
            throw new RuntimeException("该行业的风险配置已存在");
        }

        // 保存主表
        OnlineProductConfig entity = new OnlineProductConfig();
        BeanUtils.copyProperties(vo, entity);
        entity.setEnabled(vo.getEnabled() ? 1 : 0);
        
        // 转换行业代码列表为紧凑存储格式
        if (vo.getIndustryCode() != null && !vo.getIndustryCode().isEmpty()) {
            try {
                entity.setIndustryCode(convertIndustryCodeToStorage(vo.getIndustryCode()));
            } catch (Exception e) {
                throw new RuntimeException("行业代码列表转换失败", e);
            }
        }
        
        // 转换险种类型列表为JSON字符串
        if (vo.getInsuranceTypes() != null && !vo.getInsuranceTypes().isEmpty()) {
            try {
                entity.setInsuranceTypes(objectMapper.writeValueAsString(vo.getInsuranceTypes()));
            } catch (Exception e) {
                throw new RuntimeException("险种类型列表转换失败", e);
            }
        }
        
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreateUser(BscUserUtils.getUserId());
        entity.setUpdateUser(BscUserUtils.getUserId());
        entity.setTenantId(SysLoginUtils.getUser().getTenantId());
        entity.setIsDeleted(0);

        insertSelective(entity);

        return getById(entity.getId());
    }

    /**
     * 更新线上产品配置
     * @param vo 配置信息
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public OnlineProductConfigVO update(OnlineProductConfigVO vo) {
        // 检查配置是否重复
        if (checkConfigExists(vo.getIndustryCode(), vo.getProbability(), vo.getImpact(), vo.getLevel(), vo.getId())) {
            throw new RuntimeException("该行业的风险配置已存在");
        }

        // 更新主表
        OnlineProductConfig entity = new OnlineProductConfig();
        BeanUtils.copyProperties(vo, entity);
        entity.setEnabled(vo.getEnabled() ? 1 : 0);
        
        // 转换行业代码列表为紧凑存储格式
        if (vo.getIndustryCode() != null && !vo.getIndustryCode().isEmpty()) {
            try {
                entity.setIndustryCode(convertIndustryCodeToStorage(vo.getIndustryCode()));
            } catch (Exception e) {
                throw new RuntimeException("行业代码列表转换失败", e);
            }
        }
        
        // 转换险种类型列表为JSON字符串
        if (vo.getInsuranceTypes() != null && !vo.getInsuranceTypes().isEmpty()) {
            try {
                entity.setInsuranceTypes(objectMapper.writeValueAsString(vo.getInsuranceTypes()));
            } catch (Exception e) {
                throw new RuntimeException("险种类型列表转换失败", e);
            }
        }
        
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUser(BscUserUtils.getUserId());

        updateByPrimaryKeySelective(entity);

        return getById(vo.getId());
    }

    /**
     * 删除线上产品配置
     * @param id 配置ID
     */
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        // 逻辑删除
        OnlineProductConfig entity = new OnlineProductConfig();
        entity.setId(id);
        entity.setIsDeleted(1);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUser(BscUserUtils.getUserId());
        return updateByPrimaryKeySelective(entity);
    }

    /**
     * 切换线上产品配置的启用/禁用状态
     * @param id 配置ID
     * @param enabled 是否启用
     */
    @Transactional(rollbackFor = Exception.class)
    public void toggleStatus(Long id, Boolean enabled) {
        // 验证配置是否存在
        OnlineProductConfig existingConfig = selectByPrimaryKey(id);
        if (existingConfig == null) {
            throw new RuntimeException("配置不存在");
        }
        
        // 更新状态
        OnlineProductConfig entity = new OnlineProductConfig();
        entity.setId(id);
        entity.setEnabled(enabled ? 1 : 0);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUser(BscUserUtils.getUserId());
        
        int result = updateByPrimaryKeySelective(entity);
        if (result <= 0) {
            throw new RuntimeException("状态更新失败");
        }
        
        logger.info("线上产品配置状态切换成功，ID: {}, 新状态: {}", id, enabled ? "启用" : "禁用");
    }

    /**
     * 检查配置是否存在
     * @param industryCode 行业代码
     * @param probability 风险概率
     * @param impact 风险影响
     * @param level 风险等级
     * @param id 排除的ID
     * @return 是否存在
     */
    public boolean checkConfigExists(List<String> industryCode, String probability, String impact, String level, Long id) {
        return mapper.checkConfigExists(industryCode, probability, impact, level, id) > 0;
    }

    /**
     * 将行业代码列表转换为紧凑的存储格式
     * 使用逗号分隔而不是JSON格式，减少存储空间
     */
    private String convertIndustryCodeToStorage(List<String> industryCodes) {
        if (industryCodes == null || industryCodes.isEmpty()) {
            return "";
        }
        // 使用逗号分隔，比JSON格式节省约40%空间
        return String.join(",", industryCodes);
    }
    
    /**
     * 从存储格式解析行业代码列表
     */
    private List<String> parseIndustryCodeFromStorage(String storageValue) {
        if (!StringUtils.hasText(storageValue)) {
            return new ArrayList<>();
        }
        
        // 兼容新旧格式
        if (storageValue.startsWith("[")) {
            // JSON格式（旧格式）
            try {
                return objectMapper.readValue(storageValue, new TypeReference<List<String>>() {});
            } catch (Exception e) {
                logger.warn("解析JSON格式行业代码失败: {}", storageValue, e);
                return new ArrayList<>();
            }
        } else {
            // 逗号分隔格式（新格式）
            return Arrays.asList(storageValue.split(","));
        }
    }



    


    /**
     * 解析行业代码字符串（支持多种格式）
     */
    private List<String> parseIndustryCodes(OnlineProductConfigVO vo) {
        if (StringUtils.hasText(vo.getIndustryCodeStr())) {
            try {
                return parseIndustryCodeFromStorage(vo.getIndustryCodeStr());
            } catch (Exception e) {
                logger.warn("解析行业代码失败: {}", vo.getIndustryCodeStr(), e);
                // 降级处理，当作单个代码
                List<String> result = new ArrayList<>();
                result.add(vo.getIndustryCodeStr());
                return result;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 解析险种类型JSON字符串
     */
    private List<String> parseInsuranceTypes(OnlineProductConfigVO vo) {
        if (StringUtils.hasText(vo.getInsuranceTypesStr())) {
            try {
                return objectMapper.readValue(vo.getInsuranceTypesStr(), 
                    new TypeReference<List<String>>() {});
            } catch (Exception e) {
                logger.warn("解析险种类型JSON失败: {}", vo.getInsuranceTypesStr(), e);
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }


}
