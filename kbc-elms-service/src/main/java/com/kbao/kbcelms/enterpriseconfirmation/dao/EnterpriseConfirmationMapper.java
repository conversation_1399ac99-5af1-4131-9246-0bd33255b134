package com.kbao.kbcelms.enterpriseconfirmation.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;import com.kbao.kbcelms.enterpriseconfirmation.bean.DuplicateInfoVo;import com.kbao.kbcelms.enterpriseconfirmation.entity.EnterpriseConfirmation;
import com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationSearchVo;
import com.kbao.kbcelms.enterpriseconfirmation.bean.EnterpriseConfirmationListVo;
import com.kbao.kbcelms.enterpriseconfirmation.bean.DuplicateEnterpriseCreatorVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 企业确权Mapper接口
 * @Date 2025-08-20
 */
public interface EnterpriseConfirmationMapper extends BaseMapper<EnterpriseConfirmation, Integer> {

    /**
     * 分页查询企业确权列表（仅查询重复记录）
     */
    List<EnterpriseConfirmationListVo> getConfirmationList(EnterpriseConfirmationSearchVo param);

    /**
     * 根据统一信用代码查询记录
     */
    List<EnterpriseConfirmation> getByCreditCode(@Param("creditCode") String creditCode);

    /**
     * 获取重复企业创建人信息列表
     */
    List<DuplicateEnterpriseCreatorVo> getDuplicateCreators(@Param("creditCode") String creditCode);

    /**
     * 批量更新重复标记
     */
    int batchUpdateDuplicateFlag(@Param("creditCode") String creditCode, @Param("isDuplicate") Integer isDuplicate, @Param("isProcessed") Integer isProcessed);

    /**
     * 检查同机构是否已存在确权记录
     */
    DuplicateInfoVo getDuplicateNum(@Param("legalCode") String legalCode, @Param("creditCode") String creditCode);
}
