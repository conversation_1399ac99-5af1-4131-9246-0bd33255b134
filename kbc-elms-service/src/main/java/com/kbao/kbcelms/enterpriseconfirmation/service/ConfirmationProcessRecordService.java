package com.kbao.kbcelms.enterpriseconfirmation.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.enterpriseconfirmation.entity.ConfirmationProcessRecord;
import com.kbao.kbcelms.enterpriseconfirmation.dao.ConfirmationProcessRecordMapper;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 确权处理记录Service
 * @Date 2025-08-20
 */
@Service
public class ConfirmationProcessRecordService extends BaseSQLServiceImpl<ConfirmationProcessRecord, Integer, ConfirmationProcessRecordMapper> {

    /**
     * 根据统一信用代码查询处理记录
     */
    public List<ConfirmationProcessRecord> getByCreditCode(String creditCode) {
        return mapper.getByCreditCode(creditCode);
    }
}
