package com.kbao.kbcelms.opportunitysummary.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.common.nosql.service.TenantMongoServiceImpl;
import com.kbao.kbcelms.opportunitysummary.dao.OpportunitySummaryDao;
import com.kbao.kbcelms.opportunitysummary.model.OpportunitySummary;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.IDUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.data.mongodb.core.query.Query;

/**
 * 机会总结服务类
 * <AUTHOR>
 */
@Service
public class OpportunitySummaryService extends TenantMongoServiceImpl<OpportunitySummary, String, OpportunitySummaryDao> {
    /**
     * 新增机会总结
     * @param opportunitySummary 机会总结对象
     * @return 新增后的机会总结对象
     */
    public OpportunitySummary addOpportunitySummary(OpportunitySummary opportunitySummary) {
        // 调用工具类生成流水号并赋值给 logId
        String summaryId = IDUtils.generateBizId("OPSUMMARY");
        opportunitySummary.setId(summaryId);
        opportunitySummary.setCreateId(BscUserUtils.getUserId());
        opportunitySummary.setUpdateId(BscUserUtils.getUserId());
        opportunitySummary.setCreateTime(DateUtils.getCurrentDate());
        opportunitySummary.setUpdateTime(DateUtils.getCurrentDate());
        return this.save(opportunitySummary);
    }
    /**
     * 根据主键查询机会总结
     * @param primaryKey 主键
     * @return 机会总结对象
     */
    public OpportunitySummary getOpportunitySummary(String primaryKey) {
        return this.findById(primaryKey);
    }
    /**
     * 根据主键删除机会总结
     * @param primaryKey 主键
     */
    public OpportunitySummary deleteOpportunitySummary(String primaryKey) {
        OpportunitySummary exist = this.findById(primaryKey);
        if(exist == null){
            throw new RuntimeException("机会总结不存在");
        }
        this.remove(primaryKey);
        return exist;
    }
    /**
     * 根据机会id删除机会总结
     * @param opportunityId 机会id
     */
    public void deleteOpportunitySummaryByOpportunityId(String opportunityId) {
        this.dao.remove(new Query().addCriteria(Criteria.where("opportunityId").is(opportunityId)));
    }
    /**
     * 更新机会总结
     * @param opportunitySummary 机会总结对象
     * @return 更新后的机会总结对象
     */
    public OpportunitySummary updateOpportunitySummary(OpportunitySummary opportunitySummary) {

        OpportunitySummary exist = this.findById(opportunitySummary.getId());
        if(exist == null){
            throw new RuntimeException("机会总结不存在");
        }

        exist.setFileName(opportunitySummary.getFileName());
        exist.setFilePath(opportunitySummary.getFilePath());
        exist.setFileType(opportunitySummary.getFileType());
        exist.setUpdateId(BscUserUtils.getUserId());
        exist.setUpdateTime(DateUtils.getCurrentDate());
        this.update(exist);

        return exist;
    }
    /**
     * 分页查询机会总结
     * @param requestPage 分页请求参数，包含查询条件和分页信息
     * @return 包含机会总结列表和分页信息的 Pagination 对象
     */
    public PageInfo<OpportunitySummary> pageOpportunitySummary(PageRequest<OpportunitySummary> requestPage) {
        Query query = new Query();
        // 构建查询条件
        Criteria criteria = Criteria.where("opportunityId").is(requestPage.getParam().getOpportunityId());
        query.addCriteria(criteria);

        // 构建排序条件
        query.with(Sort.by(Sort.Direction.ASC, "createTime"));

        Pagination<OpportunitySummary> pagination = new Pagination<>();
        pagination.setPageNum(requestPage.getPageNum());
        pagination.setPageSize(requestPage.getPageSize());

        return this.page(query, pagination);
    }

    /**
     * 根据机会ID获取最近更新的一条日志
     * @param opportunityId 机会ID
     * @return 最近更新的一条日志
     */
    public OpportunitySummary getLatestOpportunitySummaryByOpportunityId(String opportunityId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("opportunityId").is(opportunityId));
        query.with(Sort.by(Sort.Order.desc("updateTime")));
        return this.findOne(query);
    }
}
