package com.kbao.kbcelms.common.service;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.OrgClientAdapter;
import com.kbao.kbcbsc.organization.entity.Organization;
import com.kbao.kbcbsc.organization.model.OrgIdReq;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcelms.common.vo.CascadeDictItem;
import com.kbao.kbcelms.user.service.UserService;
import com.kbao.kbcelms.user.vo.OrgResponseVO;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 机构字典服务
 * 用于获取机构信息并转换为级联字典格式
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
public class OrganizationDictService {

    private final static String THREE_LEVEL_ORG_CONSTANT = "ELMS_THREE_LEVEL_ORG_CACHE";

    private final static long expireTime = 60 * 60 * 24; // 1天

    @Autowired
    private UserService userService;

    @Autowired
    private OrgClientAdapter orgClientAdapter;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取机构信息级联字典
     * 查询1、2、4三级机构数据，按照CascadeDictItem标准形式返回
     * @return 机构级联字典列表
     */
    public List<CascadeDictItem> getOrganizationDict() {
        // 使用UserService中的方法获取机构数据
        List<OrgResponseVO> orgData = userService.findOrganizationData();
        
        if (EmptyUtils.isEmpty(orgData)) {
            return new ArrayList<>();
        }
        
        // 转换为CascadeDictItem格式
        return orgData.stream()
                .map(this::convertOrgToCascadeDict)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定级别的机构信息级联字典
     * @param orgTypes 机构类型数组，如 ["1", "2", "4"]
     * @return 机构级联字典列表
     */
    public List<CascadeDictItem> getOrganizationDictByTypes(String[] orgTypes) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        
        OrgIdReq orgIdReq = new OrgIdReq();
        orgIdReq.setTenantId(tenantId);
        
        // 获取全量机构树结构数据
        Result<List<Organization>> result = orgClientAdapter.getOrgList(orgIdReq);
        if (!ResultStatusEnum.isSuccess(result.getResp_code()) || EmptyUtils.isEmpty(result.getDatas())) {
            return new ArrayList<>();
        }
        
        List<CascadeDictItem> list = new ArrayList<>();
        
        // 过滤出指定级别的机构数据
        result.getDatas().forEach(org -> {
            processOrganizationByTypes(org, list, orgTypes);
        });
        
        return list;
    }

    /**
     * 转换OrgResponseVO为CascadeDictItem
     * @param org 机构响应VO
     * @return 级联字典项
     */
    private CascadeDictItem convertOrgToCascadeDict(OrgResponseVO org) {
        CascadeDictItem item = new CascadeDictItem(org.getOrgCode(), org.getOrgName());
        
        if (EmptyUtils.isNotEmpty(org.getChildren())) {
            List<CascadeDictItem> children = org.getChildren().stream()
                    .map(this::convertOrgToCascadeDict)
                    .collect(Collectors.toList());
            item.setChildren(children);
        }
        
        return item;
    }

    /**
     * 按指定机构类型处理机构数据
     * @param org 机构对象
     * @param list 结果列表
     * @param orgTypes 机构类型数组
     */
    private void processOrganizationByTypes(Organization org, List<CascadeDictItem> list, String[] orgTypes) {
        // 检查当前机构是否为指定类型
        boolean isTargetType = false;
        for (String orgType : orgTypes) {
            if (orgType.equals(org.getOrgType())) {
                isTargetType = true;
                break;
            }
        }
        
        if (isTargetType) {
            CascadeDictItem item = new CascadeDictItem(org.getOrgCode(), org.getOrgName());
            
            // 处理子机构
            if (EmptyUtils.isNotEmpty(org.getChildren())) {
                List<CascadeDictItem> children = new ArrayList<>();
                for (Organization child : org.getChildren()) {
                    processChildOrganizationByTypes(child, children, orgTypes);
                }
                if (EmptyUtils.isNotEmpty(children)) {
                    item.setChildren(children);
                }
            }
            
            list.add(item);
        } else {
            // 如果当前机构不是目标类型，继续处理子机构
            if (EmptyUtils.isNotEmpty(org.getChildren())) {
                for (Organization child : org.getChildren()) {
                    processOrganizationByTypes(child, list, orgTypes);
                }
            }
        }
    }

    /**
     * 处理子机构
     * @param org 机构对象
     * @param children 子机构列表
     * @param orgTypes 机构类型数组
     */
    private void processChildOrganizationByTypes(Organization org, List<CascadeDictItem> children, String[] orgTypes) {
        // 检查当前机构是否为指定类型
        boolean isTargetType = false;
        for (String orgType : orgTypes) {
            if (orgType.equals(org.getOrgType())) {
                isTargetType = true;
                break;
            }
        }
        
        if (isTargetType) {
            CascadeDictItem item = new CascadeDictItem(org.getOrgCode(), org.getOrgName());
            
            // 递归处理子机构
            if (EmptyUtils.isNotEmpty(org.getChildren())) {
                List<CascadeDictItem> subChildren = new ArrayList<>();
                for (Organization child : org.getChildren()) {
                    processChildOrganizationByTypes(child, subChildren, orgTypes);
                }
                if (EmptyUtils.isNotEmpty(subChildren)) {
                    item.setChildren(subChildren);
                }
            }
            
            children.add(item);
        } else {
            // 如果当前机构不是目标类型，继续处理子机构
            if (EmptyUtils.isNotEmpty(org.getChildren())) {
                for (Organization child : org.getChildren()) {
                    processChildOrganizationByTypes(child, children, orgTypes);
                }
            }
        }
    }

    /**
     * 获取1、2、4三级机构数据
     * 带Redis缓存，缓存时间1天
     * @return 机构级联字典列表
     */
    public List<CascadeDictItem> getThreeLevelOrganizationDict() {
        String tenantId = SysLoginUtils.getUser().getTenantId();

        // 生成缓存key
        String key = redisUtil.generateKey(tenantId, THREE_LEVEL_ORG_CONSTANT);

        // 先从缓存中获取
        Object value = redisUtil.get(key);
        if (EmptyUtils.isNotEmpty(value)) {
            return (List<CascadeDictItem>) value;
        }

        // 缓存中没有，查询数据库
        String[] targetOrgTypes = {"1", "2", "4"};
        List<CascadeDictItem> result = getOrganizationDictByTypes(targetOrgTypes);

        // 将结果存入缓存，缓存1天
        redisUtil.set(key, result, expireTime);

        return result;
    }
}
