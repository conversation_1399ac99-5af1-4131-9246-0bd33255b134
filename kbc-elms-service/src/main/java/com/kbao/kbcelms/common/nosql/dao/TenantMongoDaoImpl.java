package com.kbao.kbcelms.common.nosql.dao;

import com.alibaba.fastjson.JSONObject;
import com.kbao.kbcbsc.dao.nosql.BaseMongoDao;
import com.kbao.kbcbsc.dao.nosql.QueryBuilder;
import com.kbao.kbcbsc.util.GenericsUtils;
import com.kbao.kbcbsc.util.ReflectionUtils;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcucs.context.RequestContext;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import com.mongodb.client.result.UpdateResult;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

/*
 * 租户隔离mongoDao通用实现
 * @Author: luobb
 */
@Slf4j
@Component
public abstract class TenantMongoDaoImpl<T, ID extends Serializable> implements BaseMongoDao<T, ID> {
    private Class<T> entityClass;
    Map<String,Field> fieldsMap ;

    @Autowired
    protected MongoTemplate mongoTemplate;

    public TenantMongoDaoImpl() {
    }

    @PostConstruct
    private void init() {
        this.entityClass = GenericsUtils.getSuperClassGenricType(this.getClass());
        this.fieldsMap = this.getFieldsMap(entityClass.getDeclaredFields()) ;
    }
    protected void setMongoTemplate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public String getHashRingCollectionName(String collectionName, String tenantId, String userId) {
        return collectionName;
    }

    public T save(T entity) {
        setTenantId(entity);
        this.mongoTemplate.insert(entity);
        return entity;
    }

    public T saveOrUpdate(T entity) {
        setTenantId(entity);
        this.mongoTemplate.save(entity);
        return entity;
    }

    public T findById(ID id) {
        return (T)this.mongoTemplate.findById(id, this.entityClass);
    }

    public T findById(ID id, String collectionName) {
        return (T)this.mongoTemplate.findById(id, this.entityClass, collectionName);
    }

    public List<T> findAll() {
        if(EmptyUtils.isNotEmpty(fieldsMap.get("tenantId"))){
            Query query = new Query();
            query.addCriteria(Criteria.where("tenantId").is(getTenantId()));
            return this.find(query);
        }else{
            return this.mongoTemplate.findAll(this.entityClass);
        }
    }

    public List<T> findAll(String collectionName) {
        return this.mongoTemplate.findAll(this.entityClass, collectionName);
    }

    public List<T> find(Query query) {
        query = this.getTenantQuery(query);
        return this.mongoTemplate.find(query, this.entityClass);
    }

    public T findOne(Query query) {
        query = this.getTenantQuery(query);
        return (T)this.mongoTemplate.findOne(query, this.entityClass);
    }

    public long count(Query query) {
        query = this.getTenantQuery(query);
        return this.mongoTemplate.count(query, this.entityClass);
    }

    public UpdateResult update(Query query, Update update) {
        query = this.getTenantQuery(query);
        return update == null ? null : this.mongoTemplate.updateMulti(query, update, this.entityClass);
    }

    public T updateOne(Query query, Update update) {
        query = this.getTenantQuery(query);
        return (T)(update == null ? null : this.findAndModify(query, update, (new FindAndModifyOptions()).returnNew(true)));
    }

    public T findAndModify(Query query, Update update, FindAndModifyOptions fmo) {
        query = this.getTenantQuery(query);
        return (T)this.mongoTemplate.findAndModify(query, update, fmo, this.entityClass);
    }

    public UpdateResult update(T entity) {
        Field[] fields = this.entityClass.getDeclaredFields();
        if (fields != null && fields.length > 0) {
            Field idField = null;

            for(Field field : fields) {
                if (field.getAnnotation(Id.class) != null) {
                    idField = field;
                    break;
                }
            }

            if (idField == null) {
                return null;
            } else {
                idField.setAccessible(true);
                Object id = null;

                try {
                    id = idField.get(entity);
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }

                if (id != null && !"".equals(id.toString().trim())) {
                    Query query = new Query(Criteria.where(idField.getName()).is(id));
                    Update update = ReflectionUtils.getUpdateObj(entity);
                    return update == null ? null : this.mongoTemplate.updateFirst(query, update, this.entityClass);
                } else {
                    return null;
                }
            }
        } else {
            return null;
        }
    }

    public void remove(Query query) {
        query = this.getTenantQuery(query);
        this.mongoTemplate.remove(query, this.entityClass);
    }

    public void remove(ID id) {
        Query query = new Query(Criteria.where("_id").is(id));
        this.mongoTemplate.remove(query, this.entityClass);
    }

    public List<T> find(Query query, String sorts) {
        query = this.getTenantQuery(query);
        return this.find(query, sorts, (String)null);
    }

    public List findDistinct(String field, Map qryMap) {
        Query query = new Query();
        query.getQueryObject().putAll(qryMap);
        query = this.getTenantQuery(query);
        return this.mongoTemplate.findDistinct(query, field, this.entityClass, String.class);
    }

    public List<T> find(Query query, String sorts, String fields) {
        QueryBuilder qb = new QueryBuilder();
        query = this.getTenantQuery(query);
        qb.setQuery(query);
        qb.setIncludeFields(fields);
        qb.setSorts(sorts);
        return this.mongoTemplate.find(qb.buildQuery(), this.entityClass);
    }

    public List<JSONObject> aggregate(Aggregation aggregation, String collectionName) {
        AggregationResults<JSONObject> results = this.mongoTemplate.aggregate(aggregation, collectionName, JSONObject.class);
        return results.getMappedResults();
    }

    public void setTenantId(Object obj){
        Class<?> clazz = obj.getClass();
        try {
            Field tenantId = clazz.getDeclaredField("tenantId");
            tenantId.setAccessible(Boolean.TRUE);

            Object setObj = ReflectionUtils.getUpdateObj(obj).getUpdateObject().get("$set");
            Object tenantIdValue = ((Map<?, ?>) setObj).get("tenantId");

            if(EmptyUtils.isNotEmpty(tenantIdValue)){
                tenantId.set(obj, tenantIdValue.toString());
            }else{
                tenantId.set(obj, getTenantId());
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("设置租户字段有误,class={}",obj.getClass().getName(),e);
        }
    }

    private Map<String, Field> getFieldsMap(Field[] declaredFields) {
        Map<String, Field> map = new HashedMap();
        for (int i = 0; i <declaredFields.length ; i++) {
            map.put(declaredFields[i].getName(),declaredFields[i]);
        }
        return  map;
    }

    private String getTenantId(){
        return ElmsContext.getTenantId();
    }

    private String getUserId(){
        return RequestContext.UserId.get();
    }

    public Query getTenantQuery(Query query){
        if(EmptyUtils.isNotEmpty(this.fieldsMap.get("tenantId")) && !query.getQueryObject().containsKey("tenantId")){
            query.addCriteria(Criteria.where("tenantId").is(getTenantId()));
        }
        return query;
    }
}
