package com.kbao.kbcelms.industrylimit.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.service.IndustryService;
import com.kbao.kbcelms.industrylimit.bean.EnterpriseServiceMatchResponse;
import com.kbao.kbcelms.industrylimit.bean.EnterpriseServiceDetailResponse;
import com.kbao.kbcelms.industrylimit.dao.IndustryLimitActionMapper;
import com.kbao.kbcelms.industrylimit.dao.IndustryLimitConditionMapper;
import com.kbao.kbcelms.industrylimit.dao.IndustryLimitMapper;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimit;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitAction;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitCondition;
import com.kbao.tool.util.EmptyUtils;import com.kbao.tool.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;import java.util.stream.Collectors;

/**
 * 行业限制管理API服务实现类
 * <AUTHOR>
 * @date 2025-08-21
 */
@Slf4j
@Service
public class IndustryLimitApiService extends BaseSQLServiceImpl<IndustryLimit, Long, IndustryLimitMapper> {

    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;

    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;

    @Autowired
    private IndustryLimitConditionMapper conditionMapper;

    @Autowired
    private IndustryLimitActionMapper actionMapper;

    @Autowired
    private IndustryService industryService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取启用的行业限制规则
     */
    private List<IndustryLimit> getEnabledIndustryLimitRules() {
        try {
            // 构建查询条件
            IndustryLimit queryParam = new IndustryLimit();
            queryParam.setStatus(1);
            return this.selectByParam(MapUtils.objectToMap(queryParam));
        } catch (Exception e) {
            log.error("获取行业限制规则失败", e);
            throw new RuntimeException("获取行业限制规则失败: " + e.getMessage());
        }
    }

    /**
     * 根据规则ID获取条件列表
     * @param ruleId 规则ID
     * @return 条件列表
     */
    public List<IndustryLimitCondition> getConditionsByRuleId(Long ruleId) {
        return conditionMapper.selectByRuleId(ruleId);
    }

    /**
     * 评估单个条件
     */
    private boolean evaluateCondition(
            IndustryLimitCondition condition, 
            GenAgentEnterprise agentEnterprise) {
        
        String field = condition.getField();
        String operator = condition.getOperator();
        String expectedValue = condition.getValue();
        
        // 获取实际值
        Object actualValue = getFieldValue(field, agentEnterprise);
        
        if (actualValue == null) {
            return false;
        }
        
        // 根据操作符进行比较
        return compareValues(actualValue.toString(), expectedValue, operator);
    }

    /** 
     * 获取字段值 
     */ 
    private Object getFieldValue( 
            String field, 
            GenAgentEnterprise agentEnterprise) {
        if (agentEnterprise == null || StringUtils.isEmpty(field)) {
            return null;
        }
        
        try {
            // 使用反射获取字段值
            Field declaredField = GenAgentEnterprise.class.getDeclaredField(field);
            declaredField.setAccessible(true);
            return declaredField.get(agentEnterprise);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("获取字段值失败，字段名: {}, 错误信息: {}", field, e.getMessage());
            return null;
        }
    }

    /**
     * 比较值
     */
    private boolean compareValues(String actualValue, String expectedValue, String operator) {
        if (actualValue == null || expectedValue == null) {
            return false;
        }
        
        switch (operator) {
            case "eq": // 等于
                return actualValue.equals(expectedValue);
            case "ne": // 不等于
            case "neq": // 不等于（兼容前端）
                return !actualValue.equals(expectedValue);
            case "contains": // 包含
                return actualValue.contains(expectedValue);
            case "notContains": // 不包含
                return !actualValue.contains(expectedValue);
            case "gt": // 大于
            case "greater": // 大于（兼容前端）
                return compareNumeric(actualValue, expectedValue) > 0;
            case "lt": // 小于
            case "less": // 小于（兼容前端）
                return compareNumeric(actualValue, expectedValue) < 0;
            case "gte": // 大于等于
            case "greaterOrEqual": // 大于等于（兼容前端）
                return compareNumeric(actualValue, expectedValue) >= 0;
            case "lte": // 小于等于
            case "lessOrEqual": // 小于等于（兼容前端）
                return compareNumeric(actualValue, expectedValue) <= 0;
            case "range": // 区间
                return isInRange(actualValue, expectedValue);
            case "in": // 在集合中
                return isInCollection(actualValue, expectedValue);
            case "notIn": // 不在集合中
                return !isInCollection(actualValue, expectedValue);
            default:
                log.warn("未知操作符: {}", operator);
                return false;
        }
    }

    /**
     * 数值比较
     */
    private int compareNumeric(String actualValue, String expectedValue) {
        try {
            BigDecimal actual = new BigDecimal(actualValue);
            BigDecimal expected = new BigDecimal(expectedValue);
            return actual.compareTo(expected);
        } catch (NumberFormatException e) {
            // 如果不是数值，按字符串比较
            return actualValue.compareTo(expectedValue);
        }
    }

    /**
     * 检查是否在区间内
     */
    private boolean isInRange(String actualValue, String rangeValue) {
        try {
            // 期望格式: "min,max"
            String[] range = rangeValue.split(",");
            if (range.length != 2) {
                return false;
            }
            
            BigDecimal actual = new BigDecimal(actualValue);
            BigDecimal min = new BigDecimal(range[0].trim());
            BigDecimal max = new BigDecimal(range[1].trim());
            
            return actual.compareTo(min) >= 0 && actual.compareTo(max) <= 0;
        } catch (Exception e) {
            log.warn("区间比较失败: actualValue={}, rangeValue={}", actualValue, rangeValue, e);
            return false;
        }
    }

    /**
     * 检查是否在集合中
     */
    private boolean isInCollection(String actualValue, String expectedValue) {
        try {
            if (expectedValue.contains(",")) {
                // 包含逗号，转换为集合进行判断
                Set<String> valueSet = new HashSet<>();
                String[] values = expectedValue.split(",");
                for (String value : values) {
                    valueSet.add(value.trim());
                }
                return valueSet.contains(actualValue);
            } else {
                // 不包含逗号，使用字符串模糊匹配
                return actualValue.contains(expectedValue);
            }
        } catch (Exception e) {
            log.warn("集合匹配失败: actualValue={}, expectedValue={}", actualValue, expectedValue, e);
            return false;
        }
    }

    /**
     * 根据规则ID获取服务ID列表
     */
    /**
     * 根据规则ID获取服务ID列表（区分执行和不执行）
     * @param ruleId 规则ID
     * @return 包含执行和不执行服务ID的Map
     */
    private Map<String, Set<String>> getServiceIdsByRuleIdWithType(Long ruleId) {
        Map<String, Set<String>> result = new HashMap<>();
        result.put("applyRule", new HashSet<>());
        result.put("noAction", new HashSet<>());
        
        try {
            List<IndustryLimitAction> actions = actionMapper.selectByRuleId(ruleId);
            
            for (IndustryLimitAction action : actions) {
                if (StringUtils.hasText(action.getServiceIds()) && StringUtils.hasText(action.getIsVisible())) {
                    try {
                        List<String> actionServiceIds = objectMapper.readValue(
                                action.getServiceIds(), new TypeReference<List<String>>() {});
                        
                        Set<String> targetSet = result.get(action.getIsVisible());
                        if (targetSet != null) {
                            targetSet.addAll(actionServiceIds);
                        }
                    } catch (Exception e) {
                        log.warn("解析服务ID列表失败: {}", action.getServiceIds(), e);
                    }
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取服务ID列表失败，规则ID: {}", ruleId, e);
            return result;
        }
    }
    
    /**
     * 获取企业服务详情，包括服务ID、是否可见、是否锁定
     * @param enterpriseId 企业ID
     * @return 企业服务详情响应
     */
    public EnterpriseServiceDetailResponse getEnterpriseServiceDetails(Integer enterpriseId) {
        log.info("开始获取企业服务详情，企业ID: {}", enterpriseId);
        
        // 1. 获取企业基本信息
        GenAgentEnterprise agentEnterprise = genAgentEnterpriseService.selectByPrimaryKey(enterpriseId);
        if (agentEnterprise == null) {
            log.warn("未找到企业信息，企业ID: {}", enterpriseId);
            return buildEmptyDetailResponse(enterpriseId);
        }
        return this.buildEnterpriseServiceDetails(agentEnterprise);
    }

    public EnterpriseServiceDetailResponse buildEnterpriseServiceDetails(GenAgentEnterprise agentEnterprise) {
        // 3. 构建基础响应
        EnterpriseServiceDetailResponse response = buildBaseDetailResponse(agentEnterprise);

        // 4. 获取所有启用的规则
        List<IndustryLimit> enabledRules = getEnabledIndustryLimitRules();
        if (CollectionUtils.isEmpty(enabledRules)) {
            log.info("未找到启用的行业限制规则");
            return response;
        }

        // 5. 匹配规则并收集服务详情
        Map<String, EnterpriseServiceDetailResponse.ServiceDetail> serviceDetailMap = new HashMap<>();
        List<EnterpriseServiceDetailResponse.MatchedRuleInfo> matchedRules = new ArrayList<>();

        for (IndustryLimit rule : enabledRules) {
            EnterpriseServiceDetailResponse.MatchedRuleInfo ruleInfo = matchRuleForDetail(rule, agentEnterprise);
            if (ruleInfo.getMatched()) {
                matchedRules.add(ruleInfo);
                // 处理该规则的服务详情
                processRuleServiceDetails(rule.getId(), serviceDetailMap);
            }
        }

        // 6. 设置响应结果
        response.setServiceDetails(new ArrayList<>(serviceDetailMap.values()));
        response.setMatchedRules(matchedRules);

        log.info("企业服务详情获取完成，企业ID: {}, 服务数量: {}, 匹配规则数量: {}",
                agentEnterprise.getId(), serviceDetailMap.size(), matchedRules.size());

        return response;
    }

    public boolean isServiceLimit(GenAgentEnterprise agentEnterprise) {
        EnterpriseServiceDetailResponse serviceDetails = this.buildEnterpriseServiceDetails(agentEnterprise);
        List<EnterpriseServiceDetailResponse.ServiceDetail> details = serviceDetails.getServiceDetails();
        // 如果未匹配到规则，或者服务全部锁定且不可见，则表示服务限制
        boolean isLimited = true;
        if (EmptyUtils.isNotEmpty(details)) {
            isLimited = details.stream().allMatch(detail -> (detail.getIsLocked() && !detail.getIsVisible()));
        }
        return isLimited;
    }

    public Map<String, EnterpriseServiceDetailResponse.ServiceDetail> getServiceMap(GenAgentEnterprise agentEnterprise) {
        EnterpriseServiceDetailResponse serviceDetails = this.buildEnterpriseServiceDetails(agentEnterprise);
        List<EnterpriseServiceDetailResponse.ServiceDetail> details = serviceDetails.getServiceDetails();
        if (EmptyUtils.isNotEmpty(details)) {
            return details.stream().collect(Collectors.toMap(EnterpriseServiceDetailResponse.ServiceDetail::getServiceId, detail -> detail
                , (detail1, detail2) -> detail1));
        }
        return new HashMap<>();
    }

    /**
     * 构建空的详情响应
     */
    private EnterpriseServiceDetailResponse buildEmptyDetailResponse(Integer enterpriseId) {
        EnterpriseServiceDetailResponse response = new EnterpriseServiceDetailResponse();
        response.setEnterpriseId(enterpriseId);
        response.setServiceDetails(new ArrayList<>());
        response.setMatchedRules(new ArrayList<>());
        return response;
    }
    
    /**
     * 构建基础详情响应
     */
    private EnterpriseServiceDetailResponse buildBaseDetailResponse(
            GenAgentEnterprise agentEnterprise) {
        
        EnterpriseServiceDetailResponse response = new EnterpriseServiceDetailResponse();
        response.setEnterpriseId(agentEnterprise.getId());
        response.setEnterpriseScale(agentEnterprise.getEnterpriseScale());
        
        return response;
    }
    
    /**
     * 匹配规则并返回详情信息
     */
    private EnterpriseServiceDetailResponse.MatchedRuleInfo matchRuleForDetail(
            IndustryLimit rule, 
            GenAgentEnterprise agentEnterprise) {
        
        EnterpriseServiceDetailResponse.MatchedRuleInfo matchInfo = 
                new EnterpriseServiceDetailResponse.MatchedRuleInfo();
        matchInfo.setRuleId(rule.getId());
        matchInfo.setRuleName(rule.getName());
        
        StringBuilder matchDescription = new StringBuilder();
        matchDescription.append("规则: ").append(rule.getName()).append("; ");
        
        try {
            // 1. 获取规则条件
            List<IndustryLimitCondition> conditions = getConditionsByRuleId(rule.getId());
            if (CollectionUtils.isEmpty(conditions)) {
                matchInfo.setMatched(true);
                matchDescription.append("无条件限制，默认匹配");
            } else {
                // 2. 评估所有条件
                boolean allConditionsMatch = true;
                for (IndustryLimitCondition condition : conditions) {
                    boolean conditionMatch = evaluateCondition(condition, agentEnterprise);
                    if (!conditionMatch) {
                        allConditionsMatch = false;
                        matchDescription.append("条件不匹配: ").append(condition.getField())
                                .append(" ").append(condition.getOperator())
                                .append(" ").append(condition.getValue()).append("; ");
                        break;
                    }
                }
                matchInfo.setMatched(allConditionsMatch);
                
                if (allConditionsMatch) {
                    matchDescription.append("所有条件匹配成功");
                }
            }
            
            matchInfo.setMatchDescription(matchDescription.toString());
            
        } catch (Exception e) {
            log.error("规则匹配失败，规则ID: {}", rule.getId(), e);
            matchInfo.setMatched(false);
            matchInfo.setMatchDescription("规则匹配异常: " + e.getMessage());
        }
        
        return matchInfo;
    }
    
    /**
     * 处理规则的服务详情
     */
    private void processRuleServiceDetails(Long ruleId, Map<String, EnterpriseServiceDetailResponse.ServiceDetail> serviceDetailMap) {
        try {
            List<IndustryLimitAction> actions = actionMapper.selectByRuleId(ruleId);
            
            for (IndustryLimitAction action : actions) {
                if (StringUtils.hasText(action.getServiceIds())) {
                    try {
                        List<String> actionServiceIds = objectMapper.readValue(
                                action.getServiceIds(), new TypeReference<List<String>>() {});
                        
                        for (String serviceId : actionServiceIds) {
                            EnterpriseServiceDetailResponse.ServiceDetail detail = 
                                    serviceDetailMap.computeIfAbsent(serviceId, k -> {
                                        EnterpriseServiceDetailResponse.ServiceDetail newDetail = 
                                                new EnterpriseServiceDetailResponse.ServiceDetail();
                                        newDetail.setServiceId(serviceId);
                                        newDetail.setIsVisible(true); // 默认可见
                                        newDetail.setIsLocked(false); // 默认不锁定
                                        return newDetail;
                                    });
                            
                            // 根据动作类型和可见性、锁定状态更新服务详情
                            updateServiceDetail(detail, action);
                        }
                    } catch (Exception e) {
                        log.warn("解析服务ID列表失败: {}", action.getServiceIds(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理规则服务详情失败，规则ID: {}", ruleId, e);
        }
    }
    
    /**
     * 更新服务详情
     */
    private void updateServiceDetail(EnterpriseServiceDetailResponse.ServiceDetail detail, IndustryLimitAction action) {
        // 处理可见性
        if ("N".equals(action.getIsVisible())) {
            detail.setIsVisible(false);
        }
        
        // 处理锁定状态
        if ("Y".equals(action.getIsLocked())) {
            detail.setIsLocked(true);
        }
    }
}