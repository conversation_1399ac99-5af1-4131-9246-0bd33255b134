package com.kbao.kbcelms.industrylimit.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行业限制条件 Mapper 接口
 * <AUTHOR>
 * @date 2025-08-08
 */
@Mapper
public interface IndustryLimitConditionMapper extends BaseMapper<IndustryLimitCondition, Long> {

    /**
     * 根据规则ID查询条件列表
     * @param ruleId 规则ID
     * @return 条件列表
     */
    List<IndustryLimitCondition> selectByRuleId(@Param("ruleId") Long ruleId);

    /**
     * 根据规则ID删除条件
     * @param ruleId 规则ID
     * @return 删除数量
     */
    int deleteByRuleId(@Param("ruleId") Long ruleId);
}
