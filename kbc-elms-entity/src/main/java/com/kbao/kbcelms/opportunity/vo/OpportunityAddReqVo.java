package com.kbao.kbcelms.opportunity.vo;

import cn.hutool.json.JSONObject;import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;import com.kbao.kbcelms.opportunitydetail.vo.OpportunityDetailAddVo;import lombok.Data;
@Data
public class OpportunityAddReqVo {
    private Integer opportunityId;
    private String opportunityName;
    private Integer agentEnterpriseId;
    private String opportunityType;
    private Integer status;
    private String riskReportId;

    private OpportunityDetailAddVo detail;

    private JSONObject insureInfo;
}
