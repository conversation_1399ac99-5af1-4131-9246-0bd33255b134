package com.kbao.kbcelms.common.enums;

/**
 * 级联字典枚举
 * <AUTHOR>
 */
public enum CascadeDictEnum {

    BAS_CODE("basCode", "基础地区代码"),
    INDUSTRY("industry", "行业分类"),
    ORGANIZATION("organization", "机构信息");
    
    private final String code;
    private final String desc;
    
    CascadeDictEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static CascadeDictEnum getByCode(String code) {
        for (CascadeDictEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    public static boolean isSupported(String code) {
        return getByCode(code) != null;
    }
}
