package com.kbao.kbcelms.onlineproduct.vo;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 线上产品配置视图对象
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(value = "OnlineProductConfigVO", description = "线上产品配置视图对象")
public class OnlineProductConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 行业代码列表
     */
    @ApiModelProperty(value = "行业代码列表")
    private List<String> industryCode;

    /**
     * 行业代码原始字符串（内部使用，用于从数据库接收数据）
     */
    private String industryCodeStr;



    /**
     * 风险发生概率
     */
    @ApiModelProperty(value = "风险发生概率")
    private String probability;

    /**
     * 风险影响程度
     */
    @ApiModelProperty(value = "风险影响程度")
    private String impact;

    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    private String level;

    /**
     * 险种类型列表
     */
    @ApiModelProperty(value = "险种类型列表")
    private List<String> insuranceTypes;

    /**
     * 险种类型原始字符串（内部使用，用于从数据库接收数据）
     */
    private String insuranceTypesStr;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;

    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String enabledName;

    /**
     * 描述信息
     */
    @ApiModelProperty(value = "描述信息")
    private String description;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

}
