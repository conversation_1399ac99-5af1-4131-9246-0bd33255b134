package com.kbao.kbcelms.onlineproduct.bean;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 线上产品配置查询参数
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(value = "OnlineProductConfigQuery", description = "线上产品配置查询参数")
public class OnlineProductConfigQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 行业代码列表(支持多个行业代码查询)
     */
    @ApiModelProperty(value = "行业代码列表")
    private List<String> industryCode;



    /**
     * 风险发生概率
     */
    @ApiModelProperty(value = "风险发生概率")
    private String probability;

    /**
     * 风险影响程度
     */
    @ApiModelProperty(value = "风险影响程度")
    private String impact;

    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    private String level;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
}
