<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.riskmatrix.dao.ScoreItemMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.riskmatrix.entity.ScoreItem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="DECIMAL"/>
        <result column="max_score" property="maxScore" jdbcType="INTEGER"/>
        <result column="is_formula" property="isFormula" jdbcType="INTEGER"/>
        <result column="formula_id" property="formulaId" jdbcType="BIGINT"/>
        <result column="formula_name" property="formulaName" jdbcType="VARCHAR"/>
        <result column="coefficient" property="coefficient" jdbcType="DECIMAL"/>
        <result column="enterprise_types" property="enterpriseTypes" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, code, description, category, weight, max_score, is_formula, formula_id, formula_name,
        coefficient, enterprise_types, status, create_time, update_time, create_user, update_user
    </sql>

    <sql id="Alia_Column_List">
        s.id, s.name, s.code, s.description, s.category, s.weight, s.max_score, s.formula_id,
        s.coefficient, s.enterprise_types, s.status, s.create_time, s.update_time, s.create_user, s.update_user
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where_Clause">
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="enterpriseType != null and enterpriseType != ''">
                AND FIND_IN_SET(#{enterpriseType}, enterprise_types) > 0
            </if>
            <if test="formulaId != null">
                AND formula_id = #{formulaId}
            </if>
            <if test="createUser != null and createUser != ''">
                AND create_user = #{createUser}
            </if>
        </where>
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_score_item
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据编码查询 -->
    <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_score_item
        WHERE code = #{code,jdbcType=VARCHAR}
    </select>

    <!-- 分页查询 -->
    <select id="selectByQuery" parameterType="com.kbao.kbcelms.riskmatrix.bean.ScoreItemQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="Alia_Column_List"/>, f.name as formula_name
        FROM t_score_item s left join t_formula f on s.formula_id = f.id
        <include refid="Query_Where_Clause"/>
        ORDER BY update_time DESC
    </select>

    <!-- 查询总数 -->
    <select id="countByQuery" parameterType="com.kbao.kbcelms.riskmatrix.bean.ScoreItemQuery" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM t_score_item
        <include refid="Query_Where_Clause"/>
    </select>

    <!-- 根据类别查询 -->
    <select id="selectByCategory" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_score_item
        WHERE category = #{category,jdbcType=VARCHAR}
        AND status = 1
        ORDER BY name
    </select>

    <!-- 根据ID列表查询 -->
    <select id="selectByIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_score_item
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY name
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.riskmatrix.entity.ScoreItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_score_item (
            name, code, description, category, weight, max_score, formula_id, formula_name,
            coefficient, enterprise_types, status, create_time, update_time, create_user, update_user
        ) VALUES (
            #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
            #{category,jdbcType=VARCHAR}, #{weight,jdbcType=DECIMAL}, #{maxScore,jdbcType=INTEGER},
            #{formulaId,jdbcType=BIGINT}, #{formulaName,jdbcType=VARCHAR}, #{coefficient,jdbcType=DECIMAL},
            #{enterpriseTypes,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
            #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.ScoreItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_score_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
            <if test="description != null">description,</if>
            <if test="category != null">category,</if>
            <if test="weight != null">weight,</if>
            <if test="maxScore != null">max_score,</if>
            <if test="isFormula != null">is_formula,</if>
            <if test="formulaId != null">formula_id,</if>
            <if test="formulaName != null">formula_name,</if>
            <if test="coefficient != null">coefficient,</if>
            <if test="enterpriseTypes != null">enterprise_types,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="code != null">#{code,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="category != null">#{category,jdbcType=VARCHAR},</if>
            <if test="weight != null">#{weight,jdbcType=DECIMAL},</if>
            <if test="maxScore != null">#{maxScore,jdbcType=INTEGER},</if>
            <if test="isFormula != null">#{isFormula,jdbcType=INTEGER},</if>
            <if test="formulaId != null">#{formulaId,jdbcType=BIGINT},</if>
            <if test="formulaName != null">#{formulaName,jdbcType=VARCHAR},</if>
            <if test="coefficient != null">#{coefficient,jdbcType=DECIMAL},</if>
            <if test="enterpriseTypes != null">#{enterpriseTypes,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createUser != null">#{createUser,jdbcType=VARCHAR},</if>
            <if test="updateUser != null">#{updateUser,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.riskmatrix.entity.ScoreItem">
        UPDATE t_score_item
        SET name = #{name,jdbcType=VARCHAR},
            code = #{code,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            category = #{category,jdbcType=VARCHAR},
            weight = #{weight,jdbcType=DECIMAL},
            max_score = #{maxScore,jdbcType=INTEGER},
            formula_id = #{formulaId,jdbcType=BIGINT},
            formula_name = #{formulaName,jdbcType=VARCHAR},
            coefficient = #{coefficient,jdbcType=DECIMAL},
            enterprise_types = #{enterpriseTypes,jdbcType=VARCHAR},
            status = #{status,jdbcType=TINYINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.ScoreItem">
        UPDATE t_score_item
        <set>
            <if test="name != null">name = #{name,jdbcType=VARCHAR},</if>
            <if test="code != null">code = #{code,jdbcType=VARCHAR},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            <if test="category != null">category = #{category,jdbcType=VARCHAR},</if>
            <if test="weight != null">weight = #{weight,jdbcType=DECIMAL},</if>
            <if test="maxScore != null">max_score = #{maxScore,jdbcType=INTEGER},</if>
            <if test="isFormula != null">is_formula = #{isFormula,jdbcType=INTEGER},</if>
            <if test="formulaId != null">formula_id = #{formulaId,jdbcType=BIGINT},</if>
            <if test="formulaName != null">formula_name = #{formulaName,jdbcType=VARCHAR},</if>
            <if test="coefficient != null">coefficient = #{coefficient,jdbcType=DECIMAL},</if>
            <if test="enterpriseTypes != null">enterprise_types = #{enterpriseTypes,jdbcType=VARCHAR},</if>
            <if test="status != null">status = #{status,jdbcType=TINYINT},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateUser != null">update_user = #{updateUser,jdbcType=VARCHAR},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM t_score_item
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM t_score_item
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询评分项下拉选项-->
    <select id="selectForOptions" parameterType="com.kbao.kbcelms.riskmatrix.bean.ScoreItemOptionQuery" resultMap="BaseResultMap">
        SELECT id,`name`
        FROM t_score_item
        <where>
            status = 1
            <if test="enterpriseTypes != null and enterpriseTypes != ''">
                AND enterprise_types = #{enterpriseTypes}
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    name LIKE CONCAT('%', #{keyword}, '%')
                    OR code LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY
            create_time DESC
    </select>

</mapper>
