package com.kbao.kbcelms.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 分公司统筹角色人员查询请求VO
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Data
@ApiModel(value = "BranchCoordinatorRequestVO", description = "分公司统筹角色人员查询请求VO")
public class BranchCoordinatorRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "角色性质（必填）", required = true)
    private Integer roleType;

    @ApiModelProperty(value = "机构编码（可为空）")
    private String organCode;

    @ApiModelProperty(value = "用户姓名（可为空，模糊搜索）")
    private String nickName;

    @ApiModelProperty(value = "租户id")
    private String tenantId;
} 