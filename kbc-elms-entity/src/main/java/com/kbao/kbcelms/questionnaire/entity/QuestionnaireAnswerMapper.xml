<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.questionnaire.dao.QuestionnaireAnswerMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.questionnaire.entity.QuestionnaireAnswer">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="questionnaire_id" property="questionnaireId" jdbcType="BIGINT"/>
        <result column="question_id" property="questionId" jdbcType="BIGINT"/>
        <result column="question_title" property="questionTitle" jdbcType="VARCHAR"/>
        <result column="enterprise_id" property="enterpriseId" jdbcType="BIGINT"/>
        <result column="enterprise_type" property="enterpriseType" jdbcType="VARCHAR"/>
        <result column="score_id" property="scoreId" jdbcType="BIGINT"/>
        <result column="answer_content" property="answerContent" jdbcType="LONGVARCHAR"/>
        <result column="option_value" property="optionValue" jdbcType="VARCHAR"/>
        <result column="score" property="score" jdbcType="DECIMAL"/>
        <result column="share_user_id" property="shareUserId" jdbcType="VARCHAR"/>
        <result column="submit_union_id" property="submitUnionId" jdbcType="VARCHAR"/>
        <result column="answer_time" property="answerTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, questionnaire_id, question_id, enterprise_id, answer_content, score, 
        answer_time, create_time, update_time, deleted
    </sql>

    <!-- 根据问卷ID和企业ID查询答案列表 -->
    <select id="selectAnswersByQuestionnaireAndEnterprise" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_questionnaire_answer
        WHERE questionnaire_id = #{questionnaireId} 
        AND enterprise_id = #{enterpriseId} 
        AND deleted = 0
        ORDER BY question_id ASC
    </select>

    <!-- 根据问卷ID查询所有答案统计 -->
    <select id="selectAnswerStatistics" resultType="java.util.Map">
        SELECT 
            qq.id as question_id,
            qq.title as question_title,
            qq.type as question_type,
            qa.answer_content,
            COUNT(*) as answer_count,
            AVG(qa.score) as avg_score,
            SUM(qa.score) as total_score
        FROM t_questionnaire_question qq
        LEFT JOIN t_questionnaire_answer qa ON qq.id = qa.question_id AND qa.deleted = 0
        WHERE qq.questionnaire_id = #{questionnaireId} AND qq.deleted = 0
        GROUP BY qq.id, qq.title, qq.type, qa.answer_content
        ORDER BY qq.sort_order ASC, answer_count DESC
    </select>

    <!-- 根据企业ID查询答题记录 -->
    <select id="selectAnswerRecordsByEnterprise" resultType="java.util.Map">
        SELECT 
            q.id as questionnaire_id,
            q.title as questionnaire_title,
            COUNT(DISTINCT qa.question_id) as answered_questions,
            (SELECT COUNT(*) FROM t_questionnaire_question qq WHERE qq.questionnaire_id = q.id AND qq.deleted = 0) as total_questions,
            MAX(qa.answer_time) as last_answer_time,
            SUM(qa.score) as total_score
        FROM t_questionnaire q
        LEFT JOIN t_questionnaire_answer qa ON q.id = qa.questionnaire_id AND qa.enterprise_id = #{enterpriseId} AND qa.deleted = 0
        WHERE q.deleted = 0
        GROUP BY q.id, q.title
        HAVING answered_questions > 0
        ORDER BY last_answer_time DESC
    </select>

    <!-- 批量插入答案 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO t_questionnaire_answer
        (questionnaire_id, question_id, question_title, enterprise_id, enterprise_type, score_id, answer_content,option_value, score,
         share_user_id, submit_union_id, answer_time,  create_time, update_time, deleted)
        VALUES
        <foreach collection="answers" item="answer" separator=",">
            (#{answer.questionnaireId}, #{answer.questionId}, #{answer.questionTitle}, #{answer.enterpriseId},
             #{answer.enterpriseType},#{answer.scoreId},#{answer.answerContent},#{answer.optionValue}, #{answer.score},
             #{answer.shareUserId}, #{answer.submitUnionId}, #{answer.answerTime},
             #{answer.createTime}, #{answer.updateTime}, #{answer.deleted})
        </foreach>
    </insert>

    <!-- 统计问卷答题企业数量 -->
    <select id="countAnsweredEnterprises" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT enterprise_id)
        FROM t_questionnaire_answer
        WHERE enterprise_id = #{agentEnterpriseId} AND deleted = 0
    </select>

    <!-- 根据评分项ID列表和企业ID查询答案 -->
    <select id="selectAnswersByScoreIdsAndEnterprise" resultMap="BaseResultMap">
        SELECT
            qa.id, qa.questionnaire_id, qa.question_id, qa.question_title, qa.enterprise_id,qa.score_id,
            qa.answer_content, qa.option_value, qa.score, qa.answer_time, qa.create_time, qa.update_time, qa.deleted
        FROM t_questionnaire_answer qa
        LEFT JOIN t_questionnaire q on qa.questionnaire_id = q.id
        WHERE qa.score_id IN
        <foreach collection="scoreIds" item="scoreId" open="(" separator="," close=")">
            #{scoreId}
        </foreach>
        AND qa.enterprise_id = #{enterpriseId}
        AND qa.enterprise_type = #{enterpriseType}
        AND qa.deleted = 0
        AND q.deleted = 0
        ORDER BY qa.question_id ASC
    </select>

    <!-- 基础CRUD操作 -->

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_questionnaire_answer
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireAnswer" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_questionnaire_answer (
            questionnaire_id, question_id, enterprise_id, answer_content, score, answer_time,
            create_time, update_time, deleted
        ) VALUES (
            #{questionnaireId}, #{questionId}, #{enterpriseId}, #{answerContent}, #{score}, #{answerTime},
            #{createTime}, #{updateTime}, #{deleted}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireAnswer" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_questionnaire_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionnaireId != null">questionnaire_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="enterpriseId != null">enterprise_id,</if>
            <if test="answerContent != null">answer_content,</if>
            <if test="score != null">score,</if>
            <if test="answerTime != null">answer_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="questionnaireId != null">#{questionnaireId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="enterpriseId != null">#{enterpriseId},</if>
            <if test="answerContent != null">#{answerContent},</if>
            <if test="score != null">#{score},</if>
            <if test="answerTime != null">#{answerTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireAnswer">
        UPDATE t_questionnaire_answer SET
            questionnaire_id = #{questionnaireId},
            question_id = #{questionId},
            enterprise_id = #{enterpriseId},
            answer_content = #{answerContent},
            score = #{score},
            answer_time = #{answerTime},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.questionnaire.entity.QuestionnaireAnswer">
        UPDATE t_questionnaire_answer
        <set>
            <if test="questionnaireId != null">questionnaire_id = #{questionnaireId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="enterpriseId != null">enterprise_id = #{enterpriseId},</if>
            <if test="answerContent != null">answer_content = #{answerContent},</if>
            <if test="score != null">score = #{score},</if>
            <if test="answerTime != null">answer_time = #{answerTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据主键删除（逻辑删除） -->
    <update id="deleteByPrimaryKey">
        UPDATE t_questionnaire_answer SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 企业的问卷答案(逻辑删除) -->
    <delete id="deleteByQuestionnaireAndEnterprise">
        UPDATE t_questionnaire_answer SET deleted = 1, update_time = NOW()
        WHERE questionnaire_id = #{questionnaireId}
          AND enterprise_id = #{enterpriseId}
    </delete>

</mapper>
