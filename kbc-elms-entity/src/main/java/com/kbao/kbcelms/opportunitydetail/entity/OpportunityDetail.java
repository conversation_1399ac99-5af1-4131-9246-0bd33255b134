package com.kbao.kbcelms.opportunitydetail.entity;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;import java.util.Date;import java.util.List;

/**
 * 机会明细表
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
@ApiModel(description = "机会明细实体")
public class OpportunityDetail {
    /** 主键 */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;
    
    /** 机会ID */
    @ApiModelProperty(value = "机会ID", example = "1001")
    private Integer opportunityId;
    
    /** 投保人员规模 */
    @ApiModelProperty(value = "投保人员规模", example = "500")
    private Integer insureNum;
    
    /** 是否有历史保单：0-否，1-是 */
    @ApiModelProperty(value = "是否有历史保单", example = "1", notes = "0-否，1-是")
    private String hasHistoryPolicy;
    
    /** 保单到期日期 */
    @ApiModelProperty(value = "保单到期日期", example = "2025-12-31 23:59:59")
    private Date policyExpireTime;
    
    /** 是否需要投标：0-否，1-是 */
    @ApiModelProperty(value = "是否需要投标", example = "1", notes = "0-否，1-是")
    private Integer isBid;
    
    /** 投标结果 0-失败 1-成功 */
    @ApiModelProperty(value = "投标结果", example = "1", notes = "0-失败，1-成功")
    private Integer bidResult;
    
    /** 投标开始时间 */
    @ApiModelProperty(value = "投标开始时间", example = "2025-01-15 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date bidStartDate;
    
    /** 投标结束时间 */
    @ApiModelProperty(value = "投标结束时间", example = "2025-02-15 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date bidEndDate;
    
    /** 保费预算 */
    @ApiModelProperty(value = "保费预算", example = "1000000")
    private Integer premiumBudget;
    
    /** 企业对接人 */
    @ApiModelProperty(value = "企业对接人", example = "李经理")
    private String contacter;
    
    /** 企业对接人职务 */
    @ApiModelProperty(value = "企业对接人职务", example = "人力资源总监")
    private String contacterPost;

    @ApiModelProperty(value = "是否附加生态服务：0-否，1-是", example = "1", notes = "0-否，1-是")
    private Integer attchService;
    
    /** 是否添加健康服务产品 */
    @ApiModelProperty(value = "是否添加健康服务产品", example = "1", notes = "0-否，1-是")
    private String addHealthService;
    
    /** 健康服务产品编码 */
    @ApiModelProperty(value = "健康服务产品编码", example = "HEALTH001")
    private String healthServiceCode;
    
    /** 健康服务产品名称 */
    @ApiModelProperty(value = "健康服务产品名称", example = "健康体检服务")
    private String healthServiceName;
    
    /** 是否添加救援服务产品 */
    @ApiModelProperty(value = "是否添加救援服务产品", example = "1", notes = "0-否，1-是")
    private String addRescueService;
    
    /** 救援服务产品编码 */
    @ApiModelProperty(value = "救援服务产品编码", example = "RESCUE001")
    private String rescueServiceCode;
    
    /** 救援服务产品名称 */
    @ApiModelProperty(value = "救援服务产品名称", example = "紧急救援服务")
    private String rescueServiceName;
    
    /** 员福险种类型 */
    @ApiModelProperty(value = "员福险种类型", example = "TYPE001,TYPE002")
    private String employeeInsuranceType;
    
    /** 综合险种类型 */
    @ApiModelProperty(value = "综合险种类型", example = "综合保险A,综合保险B")
    private String generalInsuranceType;

    /** 险种类型备注 */
    @ApiModelProperty(value = "险种类型备注", example = "特殊险种说明")
    private String insuranceTypeRemark;

    /** 备注 */
    @ApiModelProperty(value = "备注", example = "项目特殊要求说明")
    private String remark;

    private String hasInsureInfo;

    /** 组队完成时间 */
    @ApiModelProperty(value = "组队完成时间", example = "2025-01-20 10:30:00")
    private Date teamTime;
    
    /** 机会提交时间 */
    @ApiModelProperty(value = "机会提交时间", example = "2025-01-25 14:20:00")
    private Date submitTime;
    
    /** 日志更新时间 */
    @ApiModelProperty(value = "日志更新时间", example = "2025-01-26 09:15:00")
    private Date logTime;
    
    /** KYC报告生成时间 */
    @ApiModelProperty(value = "KYC报告生成时间", example = "2025-01-27 16:45:00")
    private Date kycReportTime;
    
    /** KYC报告URL */
    @ApiModelProperty(value = "KYC报告URL", example = "https://example.com/kyc-report.pdf")
    private String kycReportUrl;
    
    /** 咨询风险报告生成时间 */
    @ApiModelProperty(value = "咨询风险报告生成时间", example = "2025-01-28 11:20:00")
    private Date riskReportTime;
    
    /** 风险报告URL */
    @ApiModelProperty(value = "风险报告URL", example = "https://example.com/risk-report.pdf")
    private String riskReportUrl;

    /** 风险报告ID */
    @ApiModelProperty(value = "风险报告ID", example = "RISK_20250128_001")
    private String riskReportId;

    /** 统筹领取时间 */
    @ApiModelProperty(value = "统筹领取时间", example = "2025-01-29 09:30:00")
    private Date coordinationAcceptTime;
    
    /** 指派统筹时间 */
    @ApiModelProperty(value = "指派统筹时间", example = "2025-01-30 14:15:00")
    private String assignCoordinationTime;
    
    /** 指派项目经理时间 */
    @ApiModelProperty(value = "指派项目经理时间", example = "2025-02-01 10:00:00")
    private String assignProjectManagerTime;
    
    /** 项目人员/分工/比例信息（JSON格式） */
    @ApiModelProperty(value = "项目人员/分工/比例信息", example = "[{\"userId\":\"USER001\",\"userName\":\"张三\",\"role\":\"项目经理\",\"ratio\":0.4},{\"userId\":\"USER002\",\"userName\":\"李四\",\"role\":\"技术专家\",\"ratio\":0.3}]")
    private String projectTeamInfo;
    
    /** 机会关闭时间 */
    @ApiModelProperty(value = "机会关闭时间", example = "2025-02-15 17:30:00")
    private String closeTime;
    
    /** 项目总结时间 */
    @ApiModelProperty(value = "项目总结时间", example = "2025-02-20 15:45:00")
    private Date summaryTime;
    
    /** 排分时间 */
    @ApiModelProperty(value = "排分时间", example = "2025-02-25 13:20:00")
    private String rankingTime;
    
    /** 出单时间 */
    @ApiModelProperty(value = "出单时间", example = "2025-03-01 16:00:00")
    private String policyTime;
    
    /** 暂停时间 */
    @ApiModelProperty(value = "暂停时间", example = "2025-03-05 10:30:00")
    private String suspendTime;
    
    /** 重启时间 */
    @ApiModelProperty(value = "重启时间", example = "2025-03-10 14:15:00")
    private String restartTime;
    
    /** 创建人 当前用户ID */
    @ApiModelProperty(value = "创建人ID", example = "USER001")
    private String createId;
    
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "2025-01-15 16:30:00")
    private Date createTime;
    
    /** 更新人 */
    @ApiModelProperty(value = "更新人ID", example = "USER001")
    private String updateId;
    
    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", example = "2025-01-15 16:30:00")
    private Date updateTime;
    
    /** 租户ID */
    @ApiModelProperty(value = "租户ID", example = "TENANT001")
    private String tenantId;
    
    /** 是否删除 0 未删除  1已删除 */
    @ApiModelProperty(value = "是否删除", example = "0", notes = "0-未删除，1-已删除")
    private Integer isDeleted;

    private List<String> insureTypes;
} 