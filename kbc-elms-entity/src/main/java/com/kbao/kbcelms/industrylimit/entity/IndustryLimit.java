package com.kbao.kbcelms.industrylimit.entity;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 行业限制规则实体
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ApiModel(value = "IndustryLimit", description = "行业限制规则")
public class IndustryLimit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String name;



    /**
     * 规则描述
     */
    @ApiModelProperty(value = "规则描述")
    private String description;

    /**
     * 状态(0-禁用,1-启用)
     */
    @ApiModelProperty(value = "状态(0-禁用,1-启用)")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 是否删除(0-未删除,1-已删除)
     */
    @ApiModelProperty(value = "是否删除(0-未删除,1-已删除)")
    private Integer isDeleted;
}
