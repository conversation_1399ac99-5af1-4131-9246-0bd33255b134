package com.kbao.kbcelms.industrylimit.bean;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 行业限制规则查询参数
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ApiModel(value = "IndustryLimitQuery", description = "行业限制规则查询参数")
public class IndustryLimitQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则名称(模糊查询)
     */
    @ApiModelProperty(value = "规则名称")
    private String name;



    /**
     * 状态(0-禁用,1-启用)
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
}
