package com.kbao.kbcelms.industrylimit.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 企业服务详情匹配响应结果
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
@ApiModel(value = "EnterpriseServiceDetailResponse", description = "企业服务详情匹配响应结果")
public class EnterpriseServiceDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID")
    private Integer enterpriseId;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    /**
     * 企业统一社会信用代码
     */
    @ApiModelProperty(value = "企业统一社会信用代码")
    private String creditCode;

    /**
     * 企业行业分类代码
     */
    @ApiModelProperty(value = "企业行业分类代码")
    private String industryCode;

    /**
     * 企业行业分类名称
     */
    @ApiModelProperty(value = "企业行业分类名称")
    private String industryName;

    /**
     * 企业规模
     */
    @ApiModelProperty(value = "企业规模")
    private String enterpriseScale;

    /**
     * 服务详情列表
     */
    @ApiModelProperty(value = "服务详情列表")
    private List<ServiceDetail> serviceDetails;

    /**
     * 匹配的规则列表
     */
    @ApiModelProperty(value = "匹配的规则列表")
    private List<MatchedRuleInfo> matchedRules;

    /**
     * 服务详情信息
     */
    @Data
    @ApiModel(value = "ServiceDetail", description = "服务详情信息")
    public static class ServiceDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 服务ID
         */
        @ApiModelProperty(value = "服务ID")
        private String serviceId;

        /**
         * 服务名称
         */
        @ApiModelProperty(value = "服务名称")
        private String serviceName;

        /**
         * 是否可见(visible-可见,invisible-不可见)
         */
        @ApiModelProperty(value = "是否可见")
        private Boolean isVisible;

        /**
         * 是否锁定(visible-可见,invisible-不可见)
         */
        @ApiModelProperty(value = "是否锁定")
        private Boolean isLocked;

        /**
         * 匹配的规则ID列表
         */
        @ApiModelProperty(value = "匹配的规则ID列表")
        private List<Long> matchedRuleIds;
    }

    /**
     * 匹配规则信息
     */
    @Data
    @ApiModel(value = "MatchedRuleInfo", description = "匹配规则信息")
    public static class MatchedRuleInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 规则ID
         */
        @ApiModelProperty(value = "规则ID")
        private Long ruleId;

        /**
         * 规则名称
         */
        @ApiModelProperty(value = "规则名称")
        private String ruleName;

        /**
         * 规则编码
         */
        @ApiModelProperty(value = "规则编码")
        private String ruleCode;

        /**
         * 规则描述
         */
        @ApiModelProperty(value = "规则描述")
        private String ruleDescription;

        /**
         * 匹配的服务ID列表
         */
        @ApiModelProperty(value = "匹配的服务ID列表")
        private List<String> serviceIds;

        /**
         * 匹配结果（true-匹配成功，false-匹配失败）
         */
        @ApiModelProperty(value = "匹配结果")
        private Boolean matched;

        /**
         * 匹配说明
         */
        @ApiModelProperty(value = "匹配说明")
        private String matchDescription;
    }
}