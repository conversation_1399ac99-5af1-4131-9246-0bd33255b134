<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.industrylimit.dao.IndustryLimitConditionMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.industrylimit.entity.IndustryLimitCondition">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_id" property="ruleId" jdbcType="BIGINT"/>
        <result column="field" property="field" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="value" property="value" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, rule_id, field, operator, value, description, sort_order, create_time, tenant_id
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_industry_limit_condition
        WHERE id = #{id}
    </select>

    <!-- 根据规则ID查询条件列表 -->
    <select id="selectByRuleId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_industry_limit_condition
        WHERE rule_id = #{ruleId}
        ORDER BY sort_order ASC
    </select>

    <!-- 根据参数查询 -->
    <select id="selectByParam" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_industry_limit_condition
        <where>
            <if test="param != null">
                <if test="param.ruleId != null">
                    AND rule_id = #{param.ruleId}
                </if>
                <if test="param.field != null and param.field != ''">
                    AND field = #{param.field}
                </if>
            </if>
        </where>
        ORDER BY sort_order ASC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.industrylimit.entity.IndustryLimitCondition" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_industry_limit_condition (
            rule_id, field, operator, value, description, sort_order, create_time, tenant_id
        ) VALUES (
            #{ruleId}, #{field}, #{operator}, #{value}, #{description}, #{sortOrder}, #{createTime}, #{tenantId}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.industrylimit.entity.IndustryLimitCondition" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_industry_limit_condition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">rule_id,</if>
            <if test="field != null">field,</if>
            <if test="operator != null">operator,</if>
            <if test="value != null">value,</if>
            <if test="description != null">description,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="tenantId != null">tenant_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId},</if>
            <if test="field != null">#{field},</if>
            <if test="operator != null">#{operator},</if>
            <if test="value != null">#{value},</if>
            <if test="description != null">#{description},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
        </trim>
    </insert>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.industrylimit.entity.IndustryLimitCondition">
        UPDATE t_industry_limit_condition
        <set>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="field != null">field = #{field},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="value != null">value = #{value},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据规则ID删除条件 -->
    <delete id="deleteByRuleId" parameterType="java.lang.Long">
        DELETE FROM t_industry_limit_condition WHERE rule_id = #{ruleId}
    </delete>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM t_industry_limit_condition WHERE id = #{id}
    </delete>

</mapper>
