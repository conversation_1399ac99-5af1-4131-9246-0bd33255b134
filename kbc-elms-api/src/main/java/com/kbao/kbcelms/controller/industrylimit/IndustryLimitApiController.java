package com.kbao.kbcelms.controller.industrylimit;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.industrylimit.bean.EnterpriseServiceMatchResponse;
import com.kbao.kbcelms.industrylimit.bean.EnterpriseServiceDetailResponse;
import com.kbao.kbcelms.industrylimit.service.IndustryLimitApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 行业限制管理API控制器
 * <AUTHOR>
 * @date 2025-08-21
 */
@Slf4j
@RestController
@RequestMapping("/api/industry-limit")
@Api(tags = "行业限制管理API")
public class IndustryLimitApiController extends BaseController {

    @Autowired
    private IndustryLimitApiService industryLimitApiService;

    @ApiOperation(value = "根据企业ID获取服务详情", notes = "返回服务ID、是否可见、是否锁定等详细信息")
    @GetMapping("/enterprise/{enterpriseId}/service-details")
    public Result<EnterpriseServiceDetailResponse> getEnterpriseServiceDetails(
            @ApiParam(value = "企业ID", required = true) @PathVariable Integer enterpriseId) {
        EnterpriseServiceDetailResponse response = industryLimitApiService.getEnterpriseServiceDetails(enterpriseId);
        return Result.succeed(response, "查询成功");
    }
}