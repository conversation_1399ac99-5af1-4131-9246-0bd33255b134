package com.kbao.kbcelms.controller.industry;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.service.IndustryService;
import com.kbao.kbcelms.industry.vo.IndustryTreeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 行业分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/api/industry")
@Api(tags = "行业分类管理")
public class IndustryController extends BaseController {

    @Autowired
    private IndustryService industryService;

    /**
     * 导入行业数据
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/import")
    @LogAnnotation(module = "行业分类管理", action = "导入", desc = "导入行业数据")
    @ApiOperation(value = "导入行业数据", notes = "通过Excel文件导入行业分类数据")
    public Result<String> importIndustryData(
            @ApiParam(value = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file) {
        try {
            // 基本文件验证
            if (file == null || file.isEmpty()) {
                return Result.failed("请选择要导入的文件");
            }
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return Result.failed("请上传Excel格式的文件(.xlsx或.xls)");
            }
            // 文件大小限制（10MB）
            if (file.getSize() > 10 * 1024 * 1024) {
                return Result.failed("文件大小不能超过10MB");
            }
            
            // 调用服务层导入方法
            String result = industryService.importIndustryData(file);
            
            return Result.succeed(result, "导入成功");
            
        } catch (Exception e) {
            // 记录错误日志
            logger.error("导入行业数据失败", e);
            return Result.failed("导入失败：" + e.getMessage());
        }
    }

    @LogAnnotation(module = "行业分类管理", action = "查询", desc = "查询大类行业")
    @ApiOperation(value = "查询大类行业", notes = "查询大类行业")
    @PostMapping("/findIndustryList")
    public Result<List<Industry>> findIndustryList(@RequestBody Industry industry) {
        List<Industry> list = industryService.selectByLevel(industry.getLevel());
        return Result.succeed(list, "查询成功");
    }

    /**
     * 查询行业树形结构
     */
    @ApiOperation(value = "查询行业树形结构", notes = "查询所有行业并组装为树形结构")
    @PostMapping("/tree")
    @LogAnnotation(module = "行业分类管理", recordRequestParam = false, action = "查询", desc = "查询行业树形结构")
    public Result<List<IndustryTreeVO>> getIndustryTree() {
        List<IndustryTreeVO> tree = industryService.getIndustryTree();
        return Result.succeed(tree, "查询成功");
    }

    /**
     * 清除行业树缓存
     */
    @ApiOperation(value = "清除行业树缓存", notes = "清除Redis中的行业树缓存数据")
    @PostMapping("/clearCache")
    @LogAnnotation(module = "行业分类管理", recordRequestParam = false, action = "清除缓存", desc = "清除行业树缓存")
    public Result<Void> clearIndustryTreeCache() {
        industryService.clearIndustryTreeCache();
        return Result.succeed(null, "缓存清除成功");
    }
}
