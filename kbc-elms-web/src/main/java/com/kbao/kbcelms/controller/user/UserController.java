package com.kbao.kbcelms.controller.user;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.user.service.UserService;
import com.kbao.kbcelms.user.vo.BranchCoordinatorRequestVO;
import com.kbao.kbcelms.user.vo.BranchCoordinatorResponseVO;
import com.kbao.kbcelms.user.vo.OrgResponseVO;
import com.kbao.kbcelms.user.vo.UserAddVO;
import com.kbao.kbcelms.user.vo.UserInfoVO;
import com.kbao.kbcelms.user.vo.UserRequestVO;
import com.kbao.kbcelms.user.vo.UserResponseVO;
import com.kbao.kbcelms.user.vo.UserRoleAuthVO;
import com.kbao.kbcelms.user.vo.UserRoleVO;
import com.kbao.kbcelms.userorg.vo.UserOrgAddVO;
import com.kbao.kbcelms.userorg.vo.UserOrgResponseVO;
import com.kbao.kbcelms.userrole.vo.UserRoleRequestVO;
import com.kbao.tool.util.SysLoginUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/7/21 11:24
 */
@Slf4j
@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "搜索", desc = "搜索用户列表")
    @ApiOperation(value = "用户列表分页查询", notes = "用户列表分页查询")
    @PostMapping("/page")
    public Result<PageInfo<UserResponseVO>> page(@RequestBody PageRequest<UserRequestVO> pageRequest) {
        try {
            PageInfo<UserResponseVO> pageInfo = userService.pageUser(pageRequest);
            Result<PageInfo<UserResponseVO>> result = Result.succeed(pageInfo, "查询成功");
            return result;
        } catch (Exception e) {
            log.error("分页查询用户出错：{}", e);
            throw new BusinessException("分页查询用户出错");
        }
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "修改", desc = "启用禁用")
    @ApiOperation(value = "修改", notes = "启用禁用")
    @PostMapping("/setUserStatus")
    public Result<String> setUserStatus(@RequestBody UserRequestVO requestVO) {
        userService.setUserStatus(requestVO);
        return Result.succeed("操作成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "查询", desc = "用户详情")
    @ApiOperation(value = "用户详情", notes = "用户详情")
    @PostMapping("/detail")
    public Result<UserResponseVO> detail(@RequestBody UserRequestVO requestVO) {
        UserResponseVO responseVO = userService.detail(requestVO.getId(), requestVO.getTenantId());
        return Result.succeedWith(responseVO);
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "删除", desc = "用户删除")
    @ApiOperation(value = "删除", notes = "用户删除")
    @PostMapping("/deleteUser")
    public Result<UserResponseVO> deleteUser(@RequestBody UserRequestVO requestVO) {
        userService.deleteUser(requestVO.getUserId());
        return Result.succeed("删除成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "查询", desc = "根据云服账号获取用户信息")
    @ApiOperation(value = "查询", notes = "根据云服账号获取用户信息")
    @PostMapping("/findByBscUserName")
    public Result<UserResponseVO> findByBscUserName(@RequestBody UserRequestVO requestVO) {
        UserResponseVO user = userService.findByBscUserName(requestVO.getTenantId(), requestVO.getBscUserName());
        return Result.succeed(user, "查询成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "增改", desc = "用户增改")
    @ApiOperation(value = "增改", notes = "用户增改")
    @PostMapping("/saveOrUpdate")
    public Result<String> saveOrUpdate(@RequestBody @Valid UserAddVO addVO, BindingResult result) {
        checkValidator(result);
        userService.saveOrUpdate(addVO);
        return Result.succeed("操作成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "用户增改", desc = "总部树结构数据查询")
    @ApiOperation(value = "查询", notes = "总部树结构数据查询")
    @PostMapping("/findDepartmentData")
    public Result<List<OrgResponseVO>> findDepartmentData() {
        List<OrgResponseVO> list = userService.findDepartmentData();
        return Result.succeed(list, "查询成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "用户增改", desc = "分公司树结构数据查询")
    @ApiOperation(value = "查询", notes = "分公司树结构数据查询")
    @PostMapping("/findOrganizationData")
    public Result<List<OrgResponseVO>> findOrganizationData() {
        List<OrgResponseVO> list = userService.findOrganizationData();
        return Result.succeed(list, "查询成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "查询法人机构数据", desc = "查询法人机构数据")
    @ApiOperation(value = "查询", notes = "查询法人机构数据")
    @PostMapping("/findLegalOrgData")
    public Result<List<OrgResponseVO>> findLegalOrgData() {
        List<OrgResponseVO> list = userService.findLegalOrgData();
        return Result.succeed(list, "查询成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "查询分公司数据", desc = "查询分公司数据")
    @ApiOperation(value = "查询", notes = "查询分公司数据")
    @PostMapping("/findCompanyOrgData")
    public Result<List<OrgResponseVO>> findCompanyOrgData() {
        List<OrgResponseVO> list = userService.findCompanyOrgData();
        return Result.succeed(list, "查询成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "用户增改", desc = "根据租户ID查询法人机构数据")
    @ApiOperation(value = "查询", notes = "根据租户ID查询法人机构数据")
    @PostMapping("/findLegalOrgDataByTenantId")
    public Result<UserOrgResponseVO> findLegalOrgDataByTenantId(@RequestBody UserRequestVO requestVO) {
        UserOrgResponseVO responseVO = userService.findLegalOrgDataByTenantId(requestVO.getTenantId());
        return Result.succeed(responseVO, "查询成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "分配角色", desc = "分配用户角色")
    @ApiOperation(value = "分配用户角色", notes = "分配用户角色")
    @PostMapping("/addUserRole")
    public Result<String> addUserRole(@RequestBody UserRoleRequestVO userRoleRequestVO) {
        userService.addUserRole(userRoleRequestVO);
        return Result.succeed("角色分配成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "分配角色", desc = "删除用户角色")
    @ApiOperation(value = "删除用户角色", notes = "删除用户角色")
    @PostMapping("/deleteUserRole")
    public Result<String> deleteUserRole(@RequestBody UserRoleRequestVO userRoleRequestVO) {
        userService.deleteUserRole(userRoleRequestVO);
        return Result.succeed("角色分配成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "机构权限配置", desc = "机构权限配置")
    @ApiOperation(value = "机构权限配置", notes = "机构权限配置")
    @PostMapping("/saveOrUpdateUserOrg")
    public Result<String> saveOrUpdateUserOrg(@RequestBody UserOrgAddVO orgAddVO) {
        userService.saveOrUpdateUserOrg(orgAddVO);
        return Result.succeed("机构配置成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "查询", desc = "查询用户勾选的机构数据")
    @ApiOperation(value = "查询", notes = "查询用户勾选的机构数据")
    @PostMapping("/getCheckedUserOrg")
    public Result<List<String>> getCheckedUserOrg(@RequestBody UserRequestVO requestVO) {
        List<String> list = userService.getCheckedUserOrg(requestVO);
        return Result.succeed(list, "查询成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "查询", desc = "查询用户所属角色")
    @ApiOperation(value = "查询", notes = "查询用户所属角色")
    @PostMapping("/findRolesByUserId")
    public Result<List<UserRoleVO>> findRolesByUserId(@RequestBody UserRequestVO requestVO){
        List<UserRoleVO> list = userService.findRolesByUserId(requestVO);
        return Result.succeed(list, "查询成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "查询", desc = "查询当前用户角色权限")
    @ApiOperation(value = "查询", notes = "查询当前用户角色权限")
    @PostMapping("/getCurrentUserAuth")
    public Result<UserRoleAuthVO> getCurrentUserAuth() {
        UserRoleAuthVO userRoleAuthVO = userService.getUserRoleAuthByUserId(SysLoginUtils.getUserId(), SysLoginUtils.getUser().getTenantId());
        return Result.succeed(userRoleAuthVO, "查询成功");
    }

    @LogAnnotation(module = "用户管理", recordRequestParam = true, action = "查询", desc = "查询当前用户归属租户信息")
    @ApiOperation(value = "查询", notes = "查询当前用户归属租户信息")
    @PostMapping("/getCurrentUserTenantInfo")
    public Result<UserInfoVO> getCurrentUserTenantInfo() {
        UserInfoVO userInfoVO = userService.getCurrentUserTenantInfo();
        return Result.succeed(userInfoVO, "查询成功");
    }

    /**
     * 查询分公司统筹角色人员清单
     */
    @ApiOperation(value = "查询分公司统筹角色人员清单", notes = "根据角色性质、机构编码、姓名模糊查询分公司统筹角色人员清单")
    @PostMapping("/branchUsers")
    public Result<List<BranchCoordinatorResponseVO>> getBranchCoordinatorUsers(@RequestBody BranchCoordinatorRequestVO requestVO) {
        List<BranchCoordinatorResponseVO> users = userService.getBranchCoordinatorUsers(
            requestVO.getRoleType(),
            requestVO.getOrganCode(),
            requestVO.getNickName(), requestVO.getTenantId()
        );
        return Result.succeed(users, "查询成功");
    }
}
